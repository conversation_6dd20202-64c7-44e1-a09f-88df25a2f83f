import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider } from './context/AssetContext';
import Layout from './components/Layout/Layout';

// 简单的测试组件
const Dashboard = () => (
  <div style={{
    width: '100%',
    height: '100%',
    minHeight: 'calc(100vh - 128px)',
    background: 'white',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  }}>
    <h2>总览页面</h2>
    <p>这是总览页面</p>
    <div style={{ background: '#f0f0f0', padding: '20px', margin: '20px 0', borderRadius: '4px' }}>
      <h3>测试内容区域</h3>
      <p>这里应该显示资产统计信息</p>
      <p>内容区域现在应该充分利用可用空间</p>
    </div>
    <div style={{ background: '#e6f7ff', padding: '20px', margin: '20px 0', borderRadius: '4px' }}>
      <h3>更多内容</h3>
      <p>这是额外的测试内容，用来验证布局是否正确</p>
    </div>
  </div>
);

const AssetManagement = () => (
  <div style={{
    width: '100%',
    height: '100%',
    minHeight: 'calc(100vh - 128px)',
    background: 'white',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  }}>
    <h2>资产管理</h2>
    <p>这是资产管理页面</p>
    <div style={{ background: '#f0f0f0', padding: '20px', margin: '20px 0', borderRadius: '4px' }}>
      <h3>账户管理</h3>
      <p>这里应该显示账户列表</p>
    </div>
  </div>
);

const DataVisualization = () => (
  <div style={{
    width: '100%',
    height: '100%',
    minHeight: 'calc(100vh - 128px)',
    background: 'white',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  }}>
    <h2>数据可视化</h2>
    <p>这是数据可视化页面</p>
    <div style={{ background: '#f0f0f0', padding: '20px', margin: '20px 0', borderRadius: '4px' }}>
      <h3>图表区域</h3>
      <p>这里应该显示各种图表</p>
    </div>
  </div>
);

const InvestmentAnalysis = () => (
  <div style={{
    width: '100%',
    height: '100%',
    minHeight: 'calc(100vh - 128px)',
    background: 'white',
    padding: '20px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
  }}>
    <h2>投资分析</h2>
    <p>这是投资分析页面</p>
    <div style={{ background: '#f0f0f0', padding: '20px', margin: '20px 0', borderRadius: '4px' }}>
      <h3>分析报告</h3>
      <p>这里应该显示投资分析数据</p>
    </div>
  </div>
);

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
