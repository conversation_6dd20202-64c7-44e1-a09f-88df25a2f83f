import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import AssetManagement from './pages/AssetManagement/AssetManagement';
import AssetAnalysis from './pages/DataVisualization/DataVisualizationNew';
import Dashboard from './pages/Dashboard/DashboardNew';

// AssetManagement 和 DataVisualization 组件现在从单独的文件导入

const InvestmentAnalysis = () => (
  <div style={{
    width: '100%',
    height: '100%',
    background: 'white',
    padding: '24px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column'
  }}>
    <h2 style={{ margin: '0 0 16px 0' }}>投资分析</h2>
    <p style={{ margin: '0 0 24px 0' }}>这是投资分析页面</p>
    <div style={{
      background: '#f0f0f0',
      padding: '20px',
      borderRadius: '4px',
      flex: 1,
      display: 'flex',
      flexDirection: 'column'
    }}>
      <h3>分析报告</h3>
      <p>这里应该显示投资分析数据</p>
      <div style={{
        flex: 1,
        background: '#ddd',
        borderRadius: '4px',
        marginTop: '16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '350px'
      }}>
        <span>分析报告占位区域</span>
      </div>
    </div>
  </div>
);

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/analysis" element={<AssetAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
