import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider, useAsset } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import './App.css';

// 临时简化的AssetManagement组件
function AssetManagement() {
  const { accounts, addAccount } = useAsset();

  const handleAddAccount = () => {
    const newAccount = {
      name: `测试账户${accounts.length + 1}`,
      type: '银行卡',
      note: '这是一个测试账户'
    };
    addAccount(newAccount);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>资产管理</h1>
      <Button type="primary" onClick={handleAddAccount}>
        添加测试账户
      </Button>
      <div style={{ marginTop: '20px' }}>
        <h3>账户列表 ({accounts.length})</h3>
        {accounts.map(account => (
          <div key={account.id} style={{ padding: '10px', border: '1px solid #ddd', margin: '5px 0' }}>
            <strong>{account.name}</strong> - {account.type}
            {account.note && <div style={{ fontSize: '12px', color: '#666' }}>{account.note}</div>}
          </div>
        ))}
      </div>
    </div>
  );
}

function DataVisualization() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>数据可视化</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function InvestmentAnalysis() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>投资分析</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
