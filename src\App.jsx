import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { DataProvider } from './context/DataContext';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import AssetManagement from './pages/AssetManagement/AssetManagement';
import DataVisualization from './pages/DataVisualization/DataVisualization';
import InvestmentAnalysis from './pages/InvestmentAnalysis/InvestmentAnalysis';
import './App.css';



function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <DataProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </DataProvider>
    </ConfigProvider>
  );
}

export default App;
