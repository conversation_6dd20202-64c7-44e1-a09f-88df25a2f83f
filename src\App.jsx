import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import AssetManagement from './pages/AssetManagement/AssetManagement';
import DataVisualizationBasic from './pages/DataVisualization/DataVisualizationBasic';

// 简单的测试组件
const Dashboard = () => (
  <div style={{
    width: '100%',
    height: '100%',
    background: 'white',
    padding: '24px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column'
  }}>
    <h2 style={{ margin: '0 0 16px 0' }}>总览页面</h2>
    <p style={{ margin: '0 0 24px 0' }}>这是总览页面</p>

    <div style={{
      background: '#f0f0f0',
      padding: '20px',
      margin: '0 0 20px 0',
      borderRadius: '4px',
      flex: 1
    }}>
      <h3>测试内容区域</h3>
      <p>这里应该显示资产统计信息</p>
      <p>内容区域现在应该充分利用可用空间</p>
      <div style={{ height: '200px', background: '#ddd', borderRadius: '4px', marginTop: '16px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <span>图表占位区域</span>
      </div>
    </div>

    <div style={{
      background: '#e6f7ff',
      padding: '20px',
      borderRadius: '4px',
      flex: 1
    }}>
      <h3>更多内容</h3>
      <p>这是额外的测试内容，用来验证布局是否正确</p>
      <div style={{ height: '150px', background: '#b3d9ff', borderRadius: '4px', marginTop: '16px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <span>统计数据占位区域</span>
      </div>
    </div>
  </div>
);

// AssetManagement 和 DataVisualization 组件现在从单独的文件导入

const InvestmentAnalysis = () => (
  <div style={{
    width: '100%',
    height: '100%',
    background: 'white',
    padding: '24px',
    borderRadius: '8px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    display: 'flex',
    flexDirection: 'column'
  }}>
    <h2 style={{ margin: '0 0 16px 0' }}>投资分析</h2>
    <p style={{ margin: '0 0 24px 0' }}>这是投资分析页面</p>
    <div style={{
      background: '#f0f0f0',
      padding: '20px',
      borderRadius: '4px',
      flex: 1,
      display: 'flex',
      flexDirection: 'column'
    }}>
      <h3>分析报告</h3>
      <p>这里应该显示投资分析数据</p>
      <div style={{
        flex: 1,
        background: '#ddd',
        borderRadius: '4px',
        marginTop: '16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '350px'
      }}>
        <span>分析报告占位区域</span>
      </div>
    </div>
  </div>
);

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualizationBasic />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
