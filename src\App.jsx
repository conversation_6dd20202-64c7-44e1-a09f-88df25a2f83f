import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider, useAsset } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import './App.css';

// 简化的页面组件
function Dashboard() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>仪表板</h1>
      <p>欢迎使用家财管家！</p>
    </div>
  );
}

function AssetManagement() {
  const { accounts, addAccount } = useAsset();

  const handleAddAccount = () => {
    const newAccount = {
      name: `测试账户${accounts.length + 1}`,
      type: '银行卡',
      note: '这是一个测试账户'
    };
    addAccount(newAccount);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>资产管理</h1>
      <Button type="primary" onClick={handleAddAccount} style={{ marginBottom: '20px' }}>
        添加测试账户
      </Button>
      <div>
        <h3>账户列表 ({accounts.length})</h3>
        {accounts.map(account => (
          <div key={account.id} style={{
            padding: '10px',
            border: '1px solid #ddd',
            margin: '5px 0',
            borderRadius: '4px'
          }}>
            <strong>{account.name}</strong> - {account.type}
            {account.note && <div style={{ fontSize: '12px', color: '#666' }}>{account.note}</div>}
          </div>
        ))}
      </div>
    </div>
  );
}

function DataVisualization() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>数据可视化</h1>
      <p>图表功能开发中...</p>
    </div>
  );
}

function InvestmentAnalysis() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>投资分析</h1>
      <p>分析功能开发中...</p>
    </div>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
