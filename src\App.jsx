import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// 简单的测试组件
const Dashboard = () => <div style={{ padding: '20px' }}><h2>总览页面</h2><p>这是总览页面</p></div>;
const AssetManagement = () => <div style={{ padding: '20px' }}><h2>资产管理</h2><p>这是资产管理页面</p></div>;
const DataVisualization = () => <div style={{ padding: '20px' }}><h2>数据可视化</h2><p>这是数据可视化页面</p></div>;
const InvestmentAnalysis = () => <div style={{ padding: '20px' }}><h2>投资分析</h2><p>这是投资分析页面</p></div>;

function App() {
  return (
    <Router>
      <div style={{ padding: '20px' }}>
        <h1>家财管家</h1>
        <nav style={{ marginBottom: '20px' }}>
          <a href="/" style={{ marginRight: '10px' }}>总览</a>
          <a href="/assets" style={{ marginRight: '10px' }}>资产管理</a>
          <a href="/visualization" style={{ marginRight: '10px' }}>数据可视化</a>
          <a href="/analysis" style={{ marginRight: '10px' }}>投资分析</a>
        </nav>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/assets" element={<AssetManagement />} />
          <Route path="/visualization" element={<DataVisualization />} />
          <Route path="/analysis" element={<InvestmentAnalysis />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
