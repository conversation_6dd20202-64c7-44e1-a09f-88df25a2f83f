import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider, useAsset } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import './App.css';

// 临时简化的AssetManagement组件
function AssetManagement() {
  const { accounts, addAccount } = useAsset();

  const handleAddAccount = () => {
    const newAccount = {
      name: `测试账户${accounts.length + 1}`,
      type: '银行卡',
      note: '这是一个测试账户'
    };
    addAccount(newAccount);
    alert('账户已添加！');
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', minHeight: '400px' }}>
      <h1 style={{ color: 'red' }}>资产管理页面</h1>
      <div style={{ marginBottom: '20px' }}>
        <Button
          type="primary"
          size="large"
          onClick={handleAddAccount}
          style={{ backgroundColor: 'green', borderColor: 'green' }}
        >
          🏦 添加测试账户 🏦
        </Button>
      </div>
      <div style={{ marginTop: '20px', backgroundColor: 'white', padding: '15px', borderRadius: '8px' }}>
        <h3 style={{ color: 'blue' }}>账户列表 (总数: {accounts.length})</h3>
        {accounts.length === 0 ? (
          <p style={{ color: 'gray', fontStyle: 'italic' }}>暂无账户，请点击上方按钮添加</p>
        ) : (
          accounts.map(account => (
            <div key={account.id} style={{
              padding: '15px',
              border: '2px solid #1890ff',
              margin: '10px 0',
              borderRadius: '8px',
              backgroundColor: '#f6ffed'
            }}>
              <strong style={{ fontSize: '16px' }}>{account.name}</strong> - <span style={{ color: '#1890ff' }}>{account.type}</span>
              {account.note && <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>{account.note}</div>}
            </div>
          ))
        )}
      </div>
    </div>
  );
}

function DataVisualization() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>数据可视化</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function InvestmentAnalysis() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>投资分析</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
