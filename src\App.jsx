import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider } from './context/AssetContext';
import Layout from './components/Layout/Layout';

// 简单的测试组件
const Dashboard = () => <div style={{ padding: '20px' }}><h2>总览页面</h2><p>这是总览页面</p></div>;
const AssetManagement = () => <div style={{ padding: '20px' }}><h2>资产管理</h2><p>这是资产管理页面</p></div>;
const DataVisualization = () => <div style={{ padding: '20px' }}><h2>数据可视化</h2><p>这是数据可视化页面</p></div>;
const InvestmentAnalysis = () => <div style={{ padding: '20px' }}><h2>投资分析</h2><p>这是投资分析页面</p></div>;

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
