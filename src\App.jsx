import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

// 临时简化的页面组件
function Dashboard() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>仪表板</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function AssetManagement() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>资产管理</h1>
      <Button type="primary">测试按钮</Button>
    </div>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div>
          <nav style={{ padding: '10px', background: '#f0f0f0', marginBottom: '20px' }}>
            <a href="/" style={{ marginRight: '20px' }}>仪表板</a>
            <a href="/assets">资产管理</a>
          </nav>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/assets" element={<AssetManagement />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
}

export default App;
