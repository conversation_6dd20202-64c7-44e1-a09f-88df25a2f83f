import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, Button } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AssetProvider, useAsset } from './context/AssetContext';
import Layout from './components/Layout/Layout';
import ReactECharts from 'echarts-for-react';
import './App.css';

// 简化的页面组件
function Dashboard() {
  const { accounts, records, getTotalAssets } = useAsset();

  // 计算统计数据
  const totalAssets = getTotalAssets();
  const totalAccounts = accounts.length;
  const totalRecords = records.length;

  return (
    <div style={{ padding: '20px' }}>
      <h1>仪表板</h1>
      <p>欢迎使用家财管家！</p>

      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '16px',
        marginTop: '20px'
      }}>
        <div style={{
          padding: '20px',
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <h3>总资产</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
            ¥{totalAssets.toLocaleString()}
          </p>
        </div>

        <div style={{
          padding: '20px',
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <h3>账户数量</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
            {totalAccounts}
          </p>
        </div>

        <div style={{
          padding: '20px',
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
          backgroundColor: 'white'
        }}>
          <h3>记录总数</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
            {totalRecords}
          </p>
        </div>
      </div>

      {/* 快速操作 */}
      <div style={{ marginTop: '30px' }}>
        <h2>快速操作</h2>
        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
          <Button type="primary" onClick={() => window.location.href = '/assets'}>
            添加账户
          </Button>
          <Button onClick={() => window.location.href = '/visualization'}>
            查看图表
          </Button>
        </div>
      </div>
    </div>
  );
}

function AssetManagement() {
  const { accounts, records, addAccount, addRecord } = useAsset();
  const [activeTab, setActiveTab] = React.useState('accounts');

  const handleAddAccount = () => {
    const newAccount = {
      name: `测试账户${accounts.length + 1}`,
      type: '银行卡',
      note: '这是一个测试账户'
    };
    addAccount(newAccount);
  };

  const handleAddRecord = () => {
    if (accounts.length === 0) {
      alert('请先添加账户！');
      return;
    }

    const randomAccount = accounts[Math.floor(Math.random() * accounts.length)];
    const newRecord = {
      accountId: randomAccount.id,
      date: new Date().toISOString().split('T')[0],
      amount: Math.floor(Math.random() * 100000) + 1000,
      note: '测试记录'
    };
    addRecord(newRecord);
  };

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.name : '未知账户';
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>资产管理</h1>

      {/* 标签页切换 */}
      <div style={{ marginBottom: '20px' }}>
        <Button
          type={activeTab === 'accounts' ? 'primary' : 'default'}
          onClick={() => setActiveTab('accounts')}
          style={{ marginRight: '10px' }}
        >
          账户管理
        </Button>
        <Button
          type={activeTab === 'records' ? 'primary' : 'default'}
          onClick={() => setActiveTab('records')}
        >
          记录管理
        </Button>
      </div>

      {activeTab === 'accounts' && (
        <div>
          <Button type="primary" onClick={handleAddAccount} style={{ marginBottom: '20px' }}>
            添加测试账户
          </Button>
          <div>
            <h3>账户列表 ({accounts.length})</h3>
            {accounts.map(account => (
              <div key={account.id} style={{
                padding: '15px',
                border: '1px solid #ddd',
                margin: '10px 0',
                borderRadius: '8px',
                backgroundColor: 'white'
              }}>
                <strong style={{ fontSize: '16px' }}>{account.name}</strong> -
                <span style={{ color: '#1890ff', marginLeft: '8px' }}>{account.type}</span>
                {account.note && <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>{account.note}</div>}
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'records' && (
        <div>
          <Button type="primary" onClick={handleAddRecord} style={{ marginBottom: '20px' }}>
            添加测试记录
          </Button>
          <div>
            <h3>记录列表 ({records.length})</h3>
            {records.sort((a, b) => new Date(b.date) - new Date(a.date)).map(record => (
              <div key={record.id} style={{
                padding: '15px',
                border: '1px solid #ddd',
                margin: '10px 0',
                borderRadius: '8px',
                backgroundColor: 'white'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <strong>{getAccountName(record.accountId)}</strong>
                    <div style={{ fontSize: '12px', color: '#666' }}>{record.date}</div>
                  </div>
                  <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                    ¥{record.amount.toLocaleString()}
                  </div>
                </div>
                {record.note && <div style={{ fontSize: '12px', color: '#666', marginTop: '5px' }}>{record.note}</div>}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function DataVisualization() {
  const { accounts, records } = useAsset();

  // 生成账户类型分布数据
  const accountTypeData = accounts.reduce((acc, account) => {
    acc[account.type] = (acc[account.type] || 0) + 1;
    return acc;
  }, {});

  const pieData = Object.entries(accountTypeData).map(([type, count]) => ({
    name: type,
    value: count
  }));

  // 生成记录趋势数据
  const recordTrendData = records
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .map(record => ({
      date: record.date,
      amount: record.amount
    }));

  const pieOption = {
    title: {
      text: '账户类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '账户类型',
        type: 'pie',
        radius: '50%',
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  const lineOption = {
    title: {
      text: '资产记录趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        if (params.length > 0) {
          return `${params[0].axisValue}<br/>金额: ¥${params[0].value.toLocaleString()}`;
        }
        return '';
      }
    },
    xAxis: {
      type: 'category',
      data: recordTrendData.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w';
          }
          return value.toLocaleString();
        }
      }
    },
    series: [
      {
        name: '金额',
        type: 'line',
        data: recordTrendData.map(item => item.amount),
        smooth: true,
        lineStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
              { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1>数据可视化</h1>

      {accounts.length === 0 ? (
        <p>请先添加账户数据</p>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
          gap: '20px',
          marginTop: '20px'
        }}>
          <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #d9d9d9' }}>
            <ReactECharts option={pieOption} style={{ height: '400px' }} />
          </div>

          {records.length > 0 && (
            <div style={{ backgroundColor: 'white', padding: '20px', borderRadius: '8px', border: '1px solid #d9d9d9' }}>
              <ReactECharts option={lineOption} style={{ height: '400px' }} />
            </div>
          )}
        </div>
      )}
    </div>
  );
}

function InvestmentAnalysis() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>投资分析</h1>
      <p>分析功能开发中...</p>
    </div>
  );
}

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
