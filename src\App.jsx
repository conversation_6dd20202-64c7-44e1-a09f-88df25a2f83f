import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Layout from './components/Layout/Layout';
import Dashboard from './pages/Dashboard/Dashboard';
import AssetManagement from './pages/AssetManagement/AssetManagement';
import DataVisualization from './pages/DataVisualization/DataVisualization';
import InvestmentAnalysis from './pages/InvestmentAnalysis/InvestmentAnalysis';
import { AssetProvider } from './context/AssetContext';
import './App.css';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AssetProvider>
        <Router>
          <Layout>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/assets" element={<AssetManagement />} />
              <Route path="/visualization" element={<DataVisualization />} />
              <Route path="/analysis" element={<InvestmentAnalysis />} />
            </Routes>
          </Layout>
        </Router>
      </AssetProvider>
    </ConfigProvider>
  );
}

export default App;
