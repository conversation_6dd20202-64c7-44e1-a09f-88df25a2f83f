/**
 * 家庭财务工具 - 图表绘制模块
 * 负责所有数据可视化图表的绘制
 */

/**
 * 绘制收支趋势图
 * @param {Array} transactions 交易记录数组
 */
function drawIncomeExpenseChart(transactions) {
    try {
        console.log('开始绘制收支趋势图...');
        
        // 确保transactions是数组
        if (!Array.isArray(transactions)) {
            console.error('交易记录不是数组:', transactions);
            transactions = [];
        }
        
        // 获取最近6个月的数据
        const trends = FinanceAnalyzer.analyzeTransactionTrends(transactions, 6);
        console.log('收支趋势数据:', trends);
        
        // 准备图表数据
        const labels = trends.map(t => t.yearMonth);
        const incomeData = trends.map(t => t.income);
        const expenseData = trends.map(t => t.expenses);
        const balanceData = trends.map(t => t.balance);
        
        // 获取图表容器
        const chartCanvas = document.getElementById('income-expense-chart');
        if (!chartCanvas) {
            throw new Error('找不到图表容器: income-expense-chart');
        }
        
        console.log('找到图表容器:', chartCanvas);
        
        // 确保Chart对象存在
        if (typeof Chart === 'undefined') {
            throw new Error('Chart.js未正确加载');
        }
        
        const ctx = chartCanvas.getContext('2d');
        if (!ctx) {
            throw new Error('无法获取canvas上下文');
        }
        
        // 销毁现有图表（如果有）
        if (window.incomeExpenseChart) {
            try {
                window.incomeExpenseChart.destroy();
                console.log('已销毁旧图表');
            } catch (error) {
                console.error('销毁旧图表时出错:', error);
            }
        }
        
        console.log('准备创建新图表，数据:', {
            labels,
            incomeData,
            expenseData,
            balanceData
        });
        
        // 创建新图表
        window.incomeExpenseChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '收入',
                        data: incomeData,
                        backgroundColor: 'rgba(22, 163, 74, 0.7)',
                        borderColor: 'rgba(22, 163, 74, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    },
                    {
                        label: '支出',
                        data: expenseData,
                        backgroundColor: 'rgba(220, 38, 38, 0.7)',
                        borderColor: 'rgba(220, 38, 38, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    },
                    {
                        label: '结余',
                        data: balanceData,
                        type: 'line',
                        backgroundColor: 'rgba(37, 99, 235, 0.2)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: 'rgba(37, 99, 235, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += formatCurrency(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(226, 232, 240, 0.6)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制收支趋势图出错:', error);
    }
}

/**
 * 绘制支出分类图
 * @param {Array} transactions 交易记录数组
 * @param {string} yearMonth 年月，格式为YYYY-MM
 */
function drawExpenseCategoryChart(transactions, yearMonth) {
    try {
        // 获取类别
        const categories = StorageManager.getCategories();
        
        // 计算类别支出统计
        const categoryStats = FinanceAnalyzer.calculateCategoryStats(transactions, yearMonth);
        
        // 准备图表数据
        const labels = [];
        const data = [];
        const backgroundColors = [
            'rgba(220, 38, 38, 0.7)',
            'rgba(249, 115, 22, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(22, 163, 74, 0.7)',
            'rgba(6, 182, 212, 0.7)',
            'rgba(37, 99, 235, 0.7)',
            'rgba(124, 58, 237, 0.7)',
            'rgba(190, 24, 93, 0.7)',
            'rgba(107, 114, 128, 0.7)',
            'rgba(30, 41, 59, 0.7)'
        ];
        
        categoryStats.forEach((stat, index) => {
            const category = categories.expense.find(c => c.id === stat.categoryId);
            labels.push(category ? category.name : '未知类别');
            data.push(stat.amount);
        });
        
        // 获取图表容器
        const chartCanvas = document.getElementById('expense-category-chart');
        if (!chartCanvas) {
            console.error('找不到图表容器: expense-category-chart');
            return;
        }
        
        const ctx = chartCanvas.getContext('2d');
        
        // 销毁现有图表（如果有）
        if (window.expenseCategoryChart) {
            window.expenseCategoryChart.destroy();
        }
        
        // 创建新图表
        window.expenseCategoryChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [
                    {
                        data: data,
                        backgroundColor: backgroundColors.slice(0, data.length),
                        borderWidth: 1,
                        borderColor: '#fff',
                        hoverOffset: 10
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '65%',
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.parsed);
                                const percentage = ((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1) + '%';
                                return `${label}: ${value} (${percentage})`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制支出分类图出错:', error);
    }
}

/**
 * 绘制收入来源分析图
 * @param {Array} transactions 交易记录数组
 */
function drawIncomeSourceChart(transactions) {
    try {
        // 获取类别
        const categories = StorageManager.getCategories();
        
        // 获取最近6个月的收入交易
        const now = new Date();
        const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1);
        const recentIncomeTransactions = transactions.filter(t => 
            t.type === 'income' && new Date(t.date) >= sixMonthsAgo
        );
        
        // 按类别分组
        const categoryIncomes = {};
        recentIncomeTransactions.forEach(t => {
            if (!categoryIncomes[t.categoryId]) {
                categoryIncomes[t.categoryId] = 0;
            }
            categoryIncomes[t.categoryId] += parseFloat(t.amount);
        });
        
        // 准备图表数据
        const labels = [];
        const data = [];
        const backgroundColors = [
            'rgba(22, 163, 74, 0.7)',
            'rgba(6, 182, 212, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(37, 99, 235, 0.7)',
            'rgba(124, 58, 237, 0.7)',
            'rgba(249, 115, 22, 0.7)',
            'rgba(190, 24, 93, 0.7)',
            'rgba(107, 114, 128, 0.7)',
            'rgba(30, 41, 59, 0.7)',
            'rgba(220, 38, 38, 0.7)'
        ];
        
        Object.entries(categoryIncomes).forEach(([categoryId, amount], index) => {
            const category = categories.income.find(c => c.id === categoryId);
            labels.push(category ? category.name : '未知类别');
            data.push(amount);
        });
        
        // 获取图表容器
        const chartCanvas = document.getElementById('income-source-chart');
        if (!chartCanvas) {
            console.error('找不到图表容器: income-source-chart');
            return;
        }
        
        const ctx = chartCanvas.getContext('2d');
        
        // 销毁现有图表（如果有）
        if (window.incomeSourceChart) {
            window.incomeSourceChart.destroy();
        }
        
        // 创建新图表
        window.incomeSourceChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [
                    {
                        data: data,
                        backgroundColor: backgroundColors.slice(0, data.length),
                        borderWidth: 1,
                        borderColor: '#fff',
                        hoverOffset: 10
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: '最近6个月收入来源分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.parsed);
                                const percentage = ((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1) + '%';
                                return `${label}: ${value} (${percentage})`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制收入来源分析图出错:', error);
    }
}

/**
 * 绘制支出类别分析图
 * @param {Array} transactions 交易记录数组
 */
function drawExpenseAnalysisChart(transactions) {
    try {
        // 获取类别
        const categories = StorageManager.getCategories();
        
        // 获取最近6个月的数据
        const now = new Date();
        const monthlyData = [];
        
        // 准备月份标签
        const monthLabels = [];
        for (let i = 5; i >= 0; i--) {
            const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const yearMonth = date.toISOString().substring(0, 7);
            monthLabels.push(yearMonth);
        }
        
        // 获取主要支出类别（最多5个）
        const recentExpenseTransactions = transactions.filter(t => 
            t.type === 'expense' && monthLabels.some(ym => t.date.startsWith(ym))
        );
        
        const categoryTotals = {};
        recentExpenseTransactions.forEach(t => {
            if (!categoryTotals[t.categoryId]) {
                categoryTotals[t.categoryId] = 0;
            }
            categoryTotals[t.categoryId] += parseFloat(t.amount);
        });
        
        // 按总金额排序并获取前5个类别
        const topCategories = Object.entries(categoryTotals)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([categoryId]) => categoryId);
        
        // 准备数据集
        const datasets = [];
        const colors = [
            'rgba(220, 38, 38, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(6, 182, 212, 0.7)',
            'rgba(124, 58, 237, 0.7)',
            'rgba(22, 163, 74, 0.7)'
        ];
        
        topCategories.forEach((categoryId, index) => {
            const category = categories.expense.find(c => c.id === categoryId);
            const categoryName = category ? category.name : '未知类别';
            
            // 计算每个月的支出
            const monthlyAmounts = monthLabels.map(yearMonth => {
                const monthTransactions = recentExpenseTransactions.filter(t => 
                    t.date.startsWith(yearMonth) && t.categoryId === categoryId
                );
                return monthTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0);
            });
            
            datasets.push({
                label: categoryName,
                data: monthlyAmounts,
                backgroundColor: colors[index],
                borderColor: colors[index].replace('0.7', '1'),
                borderWidth: 1,
                borderRadius: 4
            });
        });
        
        // 获取图表容器
        const chartCanvas = document.getElementById('expense-analysis-chart');
        if (!chartCanvas) {
            console.error('找不到图表容器: expense-analysis-chart');
            return;
        }
        
        const ctx = chartCanvas.getContext('2d');
        
        // 销毁现有图表（如果有）
        if (window.expenseAnalysisChart) {
            window.expenseAnalysisChart.destroy();
        }
        
        // 创建新图表
        window.expenseAnalysisChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: monthLabels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(226, 232, 240, 0.6)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '主要支出类别月度趋势',
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += formatCurrency(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制支出类别分析图出错:', error);
    }
}

/**
 * 绘制预算与实际支出对比图
 * @param {Array} transactions 交易记录数组
 */
function drawBudgetVsActualChart(transactions) {
    try {
        // 获取当前年月
        const now = new Date();
        const currentYearMonth = now.toISOString().substring(0, 7);
        
        // 获取预算
        const budget = StorageManager.getBudget(currentYearMonth);
        
        // 获取类别
        const categories = StorageManager.getCategories();
        
        // 分析预算执行情况
        const budgetAnalysis = FinanceAnalyzer.analyzeBudget(budget, transactions, currentYearMonth);
        
        // 准备图表数据
        const labels = [];
        const budgetData = [];
        const actualData = [];
        
        Object.entries(budgetAnalysis.categories).forEach(([categoryId, data]) => {
            const category = categories.expense.find(c => c.id === categoryId);
            labels.push(category ? category.name : '未知类别');
            budgetData.push(data.budget);
            actualData.push(data.spent);
        });
        
        // 获取图表容器
        const chartCanvas = document.getElementById('budget-vs-actual-chart');
        if (!chartCanvas) {
            console.error('找不到图表容器: budget-vs-actual-chart');
            return;
        }
        
        const ctx = chartCanvas.getContext('2d');
        
        // 销毁现有图表（如果有）
        if (window.budgetVsActualChart) {
            window.budgetVsActualChart.destroy();
        }
        
        // 创建新图表
        window.budgetVsActualChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '预算',
                        data: budgetData,
                        backgroundColor: 'rgba(37, 99, 235, 0.7)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    },
                    {
                        label: '实际支出',
                        data: actualData,
                        backgroundColor: 'rgba(220, 38, 38, 0.7)',
                        borderColor: 'rgba(220, 38, 38, 1)',
                        borderWidth: 1,
                        borderRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(226, 232, 240, 0.6)'
                        },
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: `${currentYearMonth} 预算与实际支出对比`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 15
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += formatCurrency(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制预算与实际支出对比图出错:', error);
    }
}

/**
 * 绘制投资分布图
 * @param {Array} investments 投资记录数组
 */
function drawInvestmentDistributionChart(investments) {
    try {
        // 按类型分组
        const typeGroups = {};
        investments.forEach(inv => {
            if (!typeGroups[inv.type]) {
                typeGroups[inv.type] = 0;
            }
            typeGroups[inv.type] += parseFloat(inv.currentValue);
        });
        
        // 准备图表数据
        const labels = [];
        const data = [];
        const backgroundColors = [
            'rgba(37, 99, 235, 0.7)',
            'rgba(22, 163, 74, 0.7)',
            'rgba(234, 179, 8, 0.7)',
            'rgba(220, 38, 38, 0.7)',
            'rgba(6, 182, 212, 0.7)',
            'rgba(124, 58, 237, 0.7)'
        ];
        
        Object.entries(typeGroups).forEach(([type, value], index) => {
            labels.push(getInvestmentTypeText(type));
            data.push(value);
        });
        
        // 获取图表容器
        const chartCanvas = document.getElementById('investment-distribution-chart');
        if (!chartCanvas) {
            console.error('找不到图表容器: investment-distribution-chart');
            return;
        }
        
        const ctx = chartCanvas.getContext('2d');
        
        // 销毁现有图表（如果有）
        if (window.investmentDistributionChart) {
            window.investmentDistributionChart.destroy();
        }
        
        // 创建新图表
        window.investmentDistributionChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [
                    {
                        data: data,
                        backgroundColor: backgroundColors.slice(0, data.length),
                        borderWidth: 1,
                        borderColor: '#fff',
                        hoverOffset: 10
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '65%',
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.8)',
                        titleFont: {
                            size: 14,
                            weight: 'bold'
                        },
                        bodyFont: {
                            size: 13
                        },
                        padding: 12,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = formatCurrency(context.parsed);
                                const percentage = ((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100).toFixed(1) + '%';
                                return `${label}: ${value} (${percentage})`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1000,
                    easing: 'easeOutQuart'
                }
            }
        });
    } catch (error) {
        console.error('绘制投资分布图出错:', error);
    }
}

/**
 * 计算财务健康指标
 * @param {Array} transactions 交易记录数组
 * @param {Array} accounts 账户数组
 */
function calculateFinancialHealthMetrics(transactions, accounts) {
    try {
        // 计算财务健康指标
        const healthMetrics = FinanceAnalyzer.calculateFinancialHealthMetrics(transactions, accounts);
        
        // 更新储蓄率
        const savingsRate = healthMetrics.savingsRate;
        document.getElementById('savings-rate').textContent = formatPercentage(savingsRate);
        document.getElementById('savings-rate-progress').style.width = `${Math.min(savingsRate, 100)}%`;
        document.getElementById('savings-rate-progress').className = 
            savingsRate >= 20 ? 'progress-bar bg-success' : 
            savingsRate >= 10 ? 'progress-bar bg-warning' : 
            'progress-bar bg-danger';
        
        // 更新债务收入比
        const debtToIncome = healthMetrics.debtToIncomeRatio;
        document.getElementById('debt-to-income').textContent = formatPercentage(debtToIncome);
        document.getElementById('debt-to-income-progress').style.width = `${Math.min(debtToIncome, 100)}%`;
        document.getElementById('debt-to-income-progress').className = 
            debtToIncome <= 36 ? 'progress-bar bg-success' : 
            debtToIncome <= 50 ? 'progress-bar bg-warning' : 
            'progress-bar bg-danger';
        
        // 更新紧急资金覆盖月数
        const emergencyFund = healthMetrics.emergencyFundMonths;
        document.getElementById('emergency-fund').textContent = emergencyFund.toFixed(1) + '个月';
        document.getElementById('emergency-fund-progress').style.width = `${Math.min(emergencyFund / 6 * 100, 100)}%`;
        document.getElementById('emergency-fund-progress').className = 
            emergencyFund >= 6 ? 'progress-bar bg-success' : 
            emergencyFund >= 3 ? 'progress-bar bg-warning' : 
            'progress-bar bg-danger';
    } catch (error) {
        console.error('计算财务健康指标出错:', error);
    }
}

/**
 * 生成财务建议
 * @param {Array} transactions 交易记录数组
 * @param {Array} accounts 账户数组
 * @param {Array} investments 投资记录数组
 */
function generateFinancialAdvice(transactions, accounts, investments) {
    try {
        // 获取当前年月
        const now = new Date();
        const currentYearMonth = now.toISOString().substring(0, 7);
        
        // 获取预算
        const budget = StorageManager.getBudget(currentYearMonth);
        
        // 计算财务健康指标
        const healthMetrics = FinanceAnalyzer.calculateFinancialHealthMetrics(transactions, accounts);
        
        // 分析预算执行情况
        const budgetAnalysis = FinanceAnalyzer.analyzeBudget(budget, transactions, currentYearMonth);
        const investmentAnalysis = FinanceAnalyzer.analyzeInvestmentPortfolio(investments);
        
        const advice = FinanceAnalyzer.generateFinancialAdvice(
            healthMetrics,
            budgetAnalysis,
            investmentAnalysis
        );
        
        // 更新财务建议
        const adviceList = document.getElementById('advice-list');
        if (adviceList) {
            adviceList.innerHTML = '';
            
            if (advice.shortTerm.length > 0) {
                advice.shortTerm.forEach(item => {
                    const li = document.createElement('li');
                    li.textContent = item;
                    li.className = 'mb-2';
                    adviceList.appendChild(li);
                });
            } else {
                const li = document.createElement('li');
                li.textContent = '您的短期财务状况良好，继续保持！';
                li.className = 'text-success mb-2';
                adviceList.appendChild(li);
            }
        }
    } catch (error) {
        console.error('生成财务建议出错:', error);
    }
}