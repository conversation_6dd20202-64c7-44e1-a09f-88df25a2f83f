import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Typography,
  Statistic,
  Space,
  Alert
} from 'antd';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

function DataVisualizationSimple() {
  const { accounts, records } = useAsset();
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(6, 'month'),
    dayjs()
  ]);
  const [selectedAccountType, setSelectedAccountType] = useState('all');

  // 获取账户类型列表
  const accountTypes = useMemo(() => {
    const types = new Set(accounts.map(acc => acc.type));
    return Array.from(types);
  }, [accounts]);

  // 过滤记录
  const filteredRecords = useMemo(() => {
    if (!records || records.length === 0) return [];
    
    return records.filter(record => {
      try {
        const recordDate = dayjs(record.date);
        if (!recordDate.isValid()) return false;
        
        const inDateRange = recordDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');
        if (!inDateRange) return false;

        if (selectedAccountType === 'all') return true;

        const account = accounts.find(acc => acc.id === record.accountId);
        return account && account.type === selectedAccountType;
      } catch (error) {
        console.error('过滤记录错误:', error);
        return false;
      }
    });
  }, [records, accounts, dateRange, selectedAccountType]);

  // 计算统计数据
  const statistics = useMemo(() => {
    const totalIncome = filteredRecords
      .filter(record => record.amount > 0)
      .reduce((sum, record) => sum + record.amount, 0);

    const totalExpense = filteredRecords
      .filter(record => record.amount < 0)
      .reduce((sum, record) => sum + Math.abs(record.amount), 0);

    const netIncome = totalIncome - totalExpense;

    return { totalIncome, totalExpense, netIncome };
  }, [filteredRecords]);

  // 账户类型分布数据
  const accountTypeDistribution = useMemo(() => {
    const typeData = {};

    accounts.forEach(account => {
      const accountRecords = filteredRecords.filter(record => record.accountId === account.id);
      const balance = accountRecords.reduce((sum, record) => sum + record.amount, 0);

      if (!typeData[account.type]) {
        typeData[account.type] = 0;
      }
      typeData[account.type] += balance;
    });

    return Object.entries(typeData)
      .filter(([_, value]) => value > 0)
      .map(([name, value]) => ({ name, value }));
  }, [accounts, filteredRecords]);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据可视化</Title>
      
      <Alert
        message="数据可视化功能"
        description="图表功能正在加载中，当前显示基础统计信息。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 筛选器 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Col>
        <Col span={8}>
          <Select
            value={selectedAccountType}
            onChange={setSelectedAccountType}
            style={{ width: '100%' }}
            placeholder="选择账户类型"
          >
            <Option value="all">全部账户类型</Option>
            {accountTypes.map(type => (
              <Option key={type} value={type}>{type}</Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总收入"
              value={statistics.totalIncome}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="总支出"
              value={statistics.totalExpense}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="净收入"
              value={statistics.netIncome}
              precision={2}
              valueStyle={{ color: statistics.netIncome >= 0 ? '#3f8600' : '#cf1322' }}
              prefix="¥"
            />
          </Card>
        </Col>
      </Row>

      {/* 基础统计信息 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="账户类型分布">
            <Space direction="vertical" style={{ width: '100%' }}>
              {accountTypeDistribution.map(({ name, value }) => (
                <div key={name} style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <span>{name}:</span>
                  <span style={{ fontWeight: 'bold' }}>¥{value.toLocaleString()}</span>
                </div>
              ))}
              {accountTypeDistribution.length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="记录统计">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="总记录数" value={filteredRecords.length} suffix="条" />
              <Statistic title="收入记录" value={filteredRecords.filter(r => r.amount > 0).length} suffix="条" />
              <Statistic title="支出记录" value={filteredRecords.filter(r => r.amount < 0).length} suffix="条" />
              <Statistic title="账户数量" value={accounts.length} suffix="个" />
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 月度数据 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="月度统计">
            <div style={{ color: '#666' }}>
              <p>筛选条件：{selectedAccountType === 'all' ? '全部账户类型' : selectedAccountType}</p>
              <p>日期范围：{dateRange[0].format('YYYY-MM-DD')} 至 {dateRange[1].format('YYYY-MM-DD')}</p>
              <p>图表功能将在ECharts库加载完成后显示。</p>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualizationSimple;
