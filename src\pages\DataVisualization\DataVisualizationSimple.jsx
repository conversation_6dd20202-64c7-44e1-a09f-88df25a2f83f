import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Statistic,
  Space,
  Progress,
  Tag
} from 'antd';
import ReactECharts from 'echarts-for-react';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

function DataVisualizationSimple() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6); // 显示最近6个月

  // 获取账户类型列表
  const accountTypes = useMemo(() => {
    const types = new Set(accounts.map(acc => acc.type));
    return Array.from(types);
  }, [accounts]);

  // 计算每个账户在指定日期的累计余额
  const getAccountBalanceAtDate = (accountId, date) => {
    const accountRecords = records
      .filter(record => record.accountId === accountId)
      .filter(record => dayjs(record.date).isSameOrBefore(date, 'day'))
      .sort((a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf());

    return accountRecords.reduce((balance, record) => balance + record.amount, 0);
  };

  // 生成月度数据
  const monthlyData = useMemo(() => {
    if (!accounts.length || !records.length) return { months: [], data: {} };

    // 生成最近N个月的月份列表
    const months = [];
    for (let i = selectedMonths - 1; i >= 0; i--) {
      months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
    }

    // 计算每个月末各账户类型的资产总额
    const data = {};
    accountTypes.forEach(type => {
      data[type] = [];
    });
    data['总资产'] = [];

    months.forEach(month => {
      const monthEnd = dayjs(month).endOf('month');
      const typeBalances = {};
      let totalBalance = 0;

      // 计算各账户类型的余额
      accountTypes.forEach(type => {
        typeBalances[type] = 0;
      });

      accounts.forEach(account => {
        const balance = getAccountBalanceAtDate(account.id, monthEnd);
        typeBalances[account.type] = (typeBalances[account.type] || 0) + balance;
        totalBalance += balance;
      });

      // 存储数据
      accountTypes.forEach(type => {
        data[type].push(typeBalances[type] || 0);
      });
      data['总资产'].push(totalBalance);
    });

    return { months, data };
  }, [accounts, records, selectedMonths, accountTypes, getAccountBalanceAtDate]);

  // 计算当前资产统计
  const currentAssetStats = useMemo(() => {
    if (!monthlyData.months.length) return { total: 0, byType: {}, changes: {} };

    const latestMonth = monthlyData.months[monthlyData.months.length - 1];
    const latestIndex = monthlyData.months.length - 1;
    const previousIndex = latestIndex - 1;

    // 当前总资产
    const currentTotal = monthlyData.data['总资产'][latestIndex] || 0;
    const previousTotal = previousIndex >= 0 ? (monthlyData.data['总资产'][previousIndex] || 0) : 0;
    const totalChange = currentTotal - previousTotal;
    const totalChangePercent = previousTotal !== 0 ? ((totalChange / previousTotal) * 100) : 0;

    // 各类型资产
    const byType = {};
    const changes = {};
    accountTypes.forEach(type => {
      const current = monthlyData.data[type][latestIndex] || 0;
      const previous = previousIndex >= 0 ? (monthlyData.data[type][previousIndex] || 0) : 0;
      const change = current - previous;
      const changePercent = previous !== 0 ? ((change / previous) * 100) : 0;

      byType[type] = current;
      changes[type] = { amount: change, percent: changePercent };
    });

    return {
      total: currentTotal,
      byType,
      changes,
      totalChange: { amount: totalChange, percent: totalChangePercent },
      latestMonth
    };
  }, [monthlyData, accountTypes]);

  // 资产变动趋势图配置
  const assetTrendOption = {
    title: {
      text: '资产变动趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ¥' + param.value.toLocaleString() + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['总资产', ...accountTypes],
      top: 30,
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: monthlyData.months.map(month => dayjs(month).format('MM月'))
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '总资产',
        type: 'line',
        data: monthlyData.data['总资产'] || [],
        itemStyle: { color: '#1890ff' },
        lineStyle: { width: 3 },
        emphasis: { focus: 'series' }
      },
      ...accountTypes.map((type, index) => ({
        name: type,
        type: 'line',
        data: monthlyData.data[type] || [],
        itemStyle: { color: ['#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96'][index % 6] },
        emphasis: { focus: 'series' }
      }))
    ]
  };

  // 当前资产分布饼图配置
  const assetDistributionOption = {
    title: {
      text: `资产分布 (${dayjs(currentAssetStats.latestMonth).format('YYYY年MM月')})`,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '资产分布',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: Object.entries(currentAssetStats.byType)
          .filter(([_, value]) => value > 0)
          .map(([name, value]) => ({ name, value })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产数据分析</Title>

      {/* 月份筛选器 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Select
            value={selectedMonths}
            onChange={setSelectedMonths}
            style={{ width: '100%' }}
            placeholder="选择显示月份数"
          >
            <Option value={3}>最近3个月</Option>
            <Option value={6}>最近6个月</Option>
            <Option value={12}>最近12个月</Option>
            <Option value={24}>最近24个月</Option>
          </Select>
        </Col>
        <Col span={16}>
          <Space>
            <span style={{ color: '#666' }}>
              数据范围: {monthlyData.months.length > 0 ?
                `${dayjs(monthlyData.months[0]).format('YYYY年MM月')} 至 ${dayjs(monthlyData.months[monthlyData.months.length - 1]).format('YYYY年MM月')}` :
                '暂无数据'}
            </span>
          </Space>
        </Col>
      </Row>

      {/* 当前资产概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title={`总资产 (${dayjs(currentAssetStats.latestMonth).format('MM月')})`}
              value={currentAssetStats.total}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
            />
            <div style={{ marginTop: 8 }}>
              <Tag color={currentAssetStats.totalChange.amount >= 0 ? 'green' : 'red'}>
                环比 {currentAssetStats.totalChange.amount >= 0 ? '+' : ''}
                {currentAssetStats.totalChange.percent.toFixed(2)}%
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="主要资产类别">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentAssetStats.byType)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3)
                .map(([type, amount]) => (
                  <div key={type} style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{type}:</span>
                    <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                  </div>
                ))}
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="资产占比">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentAssetStats.byType)
                .sort(([,a], [,b]) => b - a)
                .map(([type, amount]) => {
                  const percentage = currentAssetStats.total > 0 ? (amount / currentAssetStats.total * 100) : 0;
                  return (
                    <div key={type}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                        <span>{type}</span>
                        <span>{percentage.toFixed(1)}%</span>
                      </div>
                      <Progress percent={percentage} size="small" showInfo={false} />
                    </div>
                  );
                })}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16}>
        <Col span={24} style={{ marginBottom: 24 }}>
          <Card title="资产变动趋势">
            <ReactECharts option={assetTrendOption} style={{ height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="当前资产分布">
            <ReactECharts option={assetDistributionOption} style={{ height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="环比变动分析">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ marginBottom: 16 }}>
                <Statistic
                  title="总资产变动"
                  value={currentAssetStats.totalChange.amount}
                  precision={2}
                  valueStyle={{ color: currentAssetStats.totalChange.amount >= 0 ? '#3f8600' : '#cf1322' }}
                  prefix={currentAssetStats.totalChange.amount >= 0 ? '+¥' : '-¥'}
                />
              </div>
              {Object.entries(currentAssetStats.changes).map(([type, change]) => (
                <div key={type} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <span>{type}</span>
                  <Space>
                    <span style={{
                      color: change.amount >= 0 ? '#3f8600' : '#cf1322',
                      fontWeight: 'bold'
                    }}>
                      {change.amount >= 0 ? '+' : ''}¥{Math.abs(change.amount).toLocaleString()}
                    </span>
                    <Tag color={change.percent >= 0 ? 'green' : 'red'} size="small">
                      {change.percent >= 0 ? '+' : ''}{change.percent.toFixed(1)}%
                    </Tag>
                  </Space>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualizationSimple;
