.layout {
  min-height: 100vh;
}

.desktop-sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
}

.desktop-sider .logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 6px;
}

.header {
  background: #001529;
  padding: 0 24px;
  display: flex;
  align-items: center;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 99;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-trigger {
  color: white;
  font-size: 18px;
  margin-right: 16px;
}

.menu-trigger:hover {
  color: #1890ff !important;
}

.content {
  margin-top: 64px;
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.mobile-drawer .ant-drawer-body {
  background: #001529;
}

.mobile-drawer .ant-menu {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-sider {
    display: none;
  }
  
  .content {
    margin-left: 0;
    padding: 16px;
  }
  
  .header {
    padding: 0 16px;
  }
}

@media (min-width: 769px) {
  .content {
    margin-left: 200px;
  }
  
  .layout .ant-layout-sider-collapsed + .ant-layout .content {
    margin-left: 80px;
  }
  
  .header {
    margin-left: 200px;
  }
  
  .layout .ant-layout-sider-collapsed + .ant-layout .header {
    margin-left: 80px;
  }
}

/* 动画效果 */
.content,
.header {
  transition: margin-left 0.2s;
}
