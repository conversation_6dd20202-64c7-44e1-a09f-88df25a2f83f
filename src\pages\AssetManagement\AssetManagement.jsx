import React, { useState, useContext } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  message
} from 'antd';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import AccountList from '../../components/AccountList/AccountList';
import RecordList from '../../components/RecordList/RecordList';
import AccountModal from '../../components/AccountModal/AccountModal';
import RecordModal from '../../components/RecordModal/RecordModal';
import { useAsset } from '../../context/AssetContext';
import { exportToCSV } from '../../utils/export';
import './AssetManagement.css';

const { Title } = Typography;

function AssetManagement() {
  const [activeTab, setActiveTab] = useState('accounts');
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [recordModalVisible, setRecordModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [editingRecord, setEditingRecord] = useState(null);

  const { accounts, records } = useAsset();

  const handleAddAccount = () => {
    setEditingAccount(null);
    setAccountModalVisible(true);
  };

  const handleEditAccount = (account) => {
    setEditingAccount(account);
    setAccountModalVisible(true);
  };

  const handleAddRecord = () => {
    setEditingRecord(null);
    setRecordModalVisible(true);
  };

  const handleEditRecord = (record) => {
    setEditingRecord(record);
    setRecordModalVisible(true);
  };

  const handleExportRecords = () => {
    if (records.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    const exportData = records.map(record => {
      const account = accounts.find(acc => acc.id === record.accountId);
      const isInvestmentAccount = account && ['基金', '理财产品', '股票'].includes(account.type);

      return {
        '日期': record.date,
        '账户名称': account ? account.name : '未知账户',
        '账户类型': account ? account.type : '',
        '标的信息': (isInvestmentAccount && record.target) ? record.target : '',
        '金额': record.amount,
        '备注': record.note || ''
      };
    });

    exportToCSV(exportData, '资产记录');
    message.success('导出成功');
  };

  const tabItems = [
    {
      key: 'accounts',
      label: '账户管理',
      children: (
        <AccountList
          onEdit={handleEditAccount}
        />
      )
    },
    {
      key: 'records',
      label: '记录管理',
      children: (
        <RecordList
          onEdit={handleEditRecord}
        />
      )
    }
  ];

  const getTabExtra = () => {
    const commonButtons = [];

    if (activeTab === 'accounts') {
      commonButtons.push(
        <Button
          key="add-account"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddAccount}
        >
          添加账户
        </Button>
      );
    } else if (activeTab === 'records') {
      commonButtons.push(
        <Button
          key="export"
          icon={<DownloadOutlined />}
          onClick={handleExportRecords}
        >
          导出记录
        </Button>,
        <Button
          key="add-record"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddRecord}
        >
          添加记录
        </Button>
      );
    }

    return <Space>{commonButtons}</Space>;
  };

  return (
    <div className="asset-management">
      <div className="page-header">
        <Title level={2}>资产管理</Title>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          tabBarExtraContent={getTabExtra()}
        />
      </Card>

      {/* 账户模态框 */}
      <AccountModal
        visible={accountModalVisible}
        onCancel={() => setAccountModalVisible(false)}
        account={editingAccount}
      />

      {/* 记录模态框 */}
      <RecordModal
        visible={recordModalVisible}
        onCancel={() => setRecordModalVisible(false)}
        record={editingRecord}
      />
    </div>
  );
}

export default AssetManagement;
