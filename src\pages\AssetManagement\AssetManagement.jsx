import React, { useState, useContext } from 'react';
import {
  Card,
  Tabs,
  Button,
  Space,
  Typography,
  message,
  Modal
} from 'antd';
import { PlusOutlined, DownloadOutlined, CopyOutlined } from '@ant-design/icons';
import AccountList from '../../components/AccountList/AccountList';
import RecordList from '../../components/RecordList/RecordList';
import AccountModal from '../../components/AccountModal/AccountModal';
import RecordModal from '../../components/RecordModal/RecordModal';
import { useAsset } from '../../context/AssetContext';
import { exportToCSV } from '../../utils/export';
import './AssetManagement.css';
import dayjs from 'dayjs';

const { Title } = Typography;

function AssetManagement() {
  const [activeTab, setActiveTab] = useState('accounts');
  const [accountModalVisible, setAccountModalVisible] = useState(false);
  const [recordModalVisible, setRecordModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [editingRecord, setEditingRecord] = useState(null);

  const { accounts, records, addRecord } = useAsset();

  const handleAddAccount = () => {
    setEditingAccount(null);
    setAccountModalVisible(true);
  };

  const handleEditAccount = (account) => {
    setEditingAccount(account);
    setAccountModalVisible(true);
  };

  const handleAddRecord = () => {
    setEditingRecord(null);
    setRecordModalVisible(true);
  };

  const handleEditRecord = (record) => {
    setEditingRecord(record);
    setRecordModalVisible(true);
  };

  const handleExportRecords = () => {
    if (records.length === 0) {
      message.warning('暂无数据可导出');
      return;
    }

    const exportData = records.map(record => {
      const account = accounts.find(acc => acc.id === record.accountId);
      const isInvestmentAccount = account && ['基金', '理财产品', '股票'].includes(account.type);

      return {
        '日期': record.date,
        '账户名称': account ? account.name : '未知账户',
        '账户类型': account ? account.type : '',
        '标的信息': (isInvestmentAccount && record.target) ? record.target : '',
        '金额': record.amount,
        '备注': record.note || ''
      };
    });

    exportToCSV(exportData, '资产记录');
    message.success('导出成功');
  };

  const handleCopyLastMonthRecords = () => {
    // 获取上个月的年月
    const lastMonth = dayjs().subtract(1, 'month');
    const lastMonthStr = lastMonth.format('YYYY-MM');

    // 筛选上个月的记录
    const lastMonthRecords = records.filter(record => {
      return record.date.startsWith(lastMonthStr);
    });

    if (lastMonthRecords.length === 0) {
      message.warning(`${lastMonth.format('YYYY年MM月')}没有找到记录`);
      return;
    }

    // 确认对话框
    Modal.confirm({
      title: '复制上月记录',
      content: `确定要复制${lastMonth.format('YYYY年MM月')}的 ${lastMonthRecords.length} 条记录到本月吗？记录日期将自动调整为本月对应日期。`,
      okText: '确定复制',
      cancelText: '取消',
      onOk: () => {
        const currentMonth = dayjs();
        let successCount = 0;
        let skipCount = 0;

        lastMonthRecords.forEach(record => {
          try {
            // 计算新的日期：保持日期，但改为当前月份
            const originalDay = dayjs(record.date).date();
            const currentMonthDaysCount = currentMonth.daysInMonth();

            // 如果原日期超过当前月份的天数（比如1月31日复制到2月），则使用当前月份的最后一天
            const newDay = originalDay > currentMonthDaysCount ? currentMonthDaysCount : originalDay;
            const newDate = currentMonth.date(newDay).format('YYYY-MM-DD');

            // 检查是否已存在相同的记录（相同账户、相同日期、相同标的）
            const existingRecord = records.find(r =>
              r.accountId === record.accountId &&
              r.date === newDate &&
              r.target === record.target
            );

            if (existingRecord) {
              skipCount++;
              return;
            }

            // 创建新记录
            const newRecord = {
              accountId: record.accountId,
              date: newDate,
              amount: record.amount,
              target: record.target,
              note: record.note ? `${record.note} (复制自${lastMonth.format('MM月')})` : `复制自${lastMonth.format('MM月')}`
            };

            addRecord(newRecord);
            successCount++;
          } catch (error) {
            console.error('复制记录失败:', error);
            skipCount++;
          }
        });

        if (successCount > 0) {
          message.success(`成功复制 ${successCount} 条记录${skipCount > 0 ? `，跳过 ${skipCount} 条重复记录` : ''}`);
        } else {
          message.warning('没有复制任何记录，可能都已存在');
        }
      }
    });
  };

  const tabItems = [
    {
      key: 'accounts',
      label: '账户管理',
      children: (
        <AccountList
          onEdit={handleEditAccount}
        />
      )
    },
    {
      key: 'records',
      label: '记录管理',
      children: (
        <RecordList
          onEdit={handleEditRecord}
        />
      )
    }
  ];

  const getTabExtra = () => {
    const commonButtons = [];

    if (activeTab === 'accounts') {
      commonButtons.push(
        <Button
          key="add-account"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddAccount}
        >
          添加账户
        </Button>
      );
    } else if (activeTab === 'records') {
      commonButtons.push(
        <Button
          key="copy-last-month"
          icon={<CopyOutlined />}
          onClick={handleCopyLastMonthRecords}
        >
          复制上月记录
        </Button>,
        <Button
          key="export"
          icon={<DownloadOutlined />}
          onClick={handleExportRecords}
        >
          导出记录
        </Button>,
        <Button
          key="add-record"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddRecord}
        >
          添加记录
        </Button>
      );
    }

    return <Space>{commonButtons}</Space>;
  };

  return (
    <div className="asset-management">
      <div className="page-header">
        <Title level={2}>资产管理</Title>
      </div>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          tabBarExtraContent={getTabExtra()}
        />
      </Card>

      {/* 账户模态框 */}
      <AccountModal
        visible={accountModalVisible}
        onCancel={() => setAccountModalVisible(false)}
        account={editingAccount}
      />

      {/* 记录模态框 */}
      <RecordModal
        visible={recordModalVisible}
        onCancel={() => setRecordModalVisible(false)}
        record={editingRecord}
      />
    </div>
  );
}

export default AssetManagement;
