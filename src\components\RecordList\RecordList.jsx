import React, { useState } from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Popconfirm,
  Empty,
  DatePicker,
  Select,
  Input,
  Row,
  Col,
  message
} from 'antd';
import { EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

function RecordList({ onEdit }) {
  const { records, accounts, deleteRecord } = useAsset();
  const [filters, setFilters] = useState({
    accountId: null,
    dateRange: null,
    searchText: ''
  });

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.name : '未知账户';
  };

  const getAccountType = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.type : '';
  };

  const getTypeColor = (type) => {
    const colorMap = {
      '银行卡': 'blue',
      '基金': 'green',
      '理财产品': 'orange',
      '股票': 'red',
      '信用卡': 'purple',
      '其他': 'default'
    };
    return colorMap[type] || 'default';
  };

  const handleDelete = (recordId) => {
    deleteRecord(recordId);
    message.success('删除成功');
  };

  // 过滤数据
  const filteredRecords = records.filter(record => {
    // 账户过滤
    if (filters.accountId && record.accountId !== filters.accountId) {
      return false;
    }

    // 日期范围过滤
    if (filters.dateRange && filters.dateRange.length === 2) {
      const recordDate = dayjs(record.date);
      if (!recordDate.isBetween(filters.dateRange[0], filters.dateRange[1], 'day', '[]')) {
        return false;
      }
    }

    // 文本搜索过滤
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      const accountName = getAccountName(record.accountId).toLowerCase();
      const note = (record.note || '').toLowerCase();
      if (!accountName.includes(searchLower) && !note.includes(searchLower)) {
        return false;
      }
    }

    return true;
  });

  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      sorter: (a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf(),
      defaultSortOrder: 'descend',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '账户名称',
      key: 'accountName',
      width: 150,
      render: (_, record) => getAccountName(record.accountId),
    },
    {
      title: '账户类型',
      key: 'accountType',
      width: 100,
      render: (_, record) => {
        const type = getAccountType(record.accountId);
        return <Tag color={getTypeColor(type)}>{type}</Tag>;
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      sorter: (a, b) => a.amount - b.amount,
      render: (amount) => (
        <span style={{ fontWeight: 'bold' }}>
          ¥{amount.toLocaleString()}
        </span>
      ),
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这条记录吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 筛选器 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={8}>
          <Select
            placeholder="选择账户"
            allowClear
            style={{ width: '100%' }}
            value={filters.accountId}
            onChange={(value) => setFilters(prev => ({ ...prev, accountId: value }))}
          >
            {accounts.map(account => (
              <Option key={account.id} value={account.id}>
                {account.name} ({account.type})
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={8}>
          <RangePicker
            style={{ width: '100%' }}
            value={filters.dateRange}
            onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
            placeholder={['开始日期', '结束日期']}
          />
        </Col>
        <Col xs={24} sm={8}>
          <Input
            placeholder="搜索账户名称或备注"
            prefix={<SearchOutlined />}
            allowClear
            value={filters.searchText}
            onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
          />
        </Col>
      </Row>

      {/* 表格 */}
      <Table
        columns={columns}
        dataSource={filteredRecords}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        }}
        locale={{
          emptyText: (
            <Empty
              description="暂无记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ),
        }}
        scroll={{ x: 800 }}
      />
    </div>
  );
}

export default RecordList;
