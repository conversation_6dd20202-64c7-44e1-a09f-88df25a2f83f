import React, { useState, useMemo } from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Popconfirm,
  Empty,
  DatePicker,
  Select,
  Input,
  Row,
  Col,
  message
} from 'antd';
import { EditOutlined, DeleteOutlined, SearchOutlined } from '@ant-design/icons';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

function RecordList({ onEdit }) {
  const { records, accounts, deleteRecord } = useAsset();
  const [filters, setFilters] = useState({
    accountId: null,
    dateRange: null,
    searchText: '',
    monthFilter: null
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100']
  });

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.name : '未知账户';
  };

  const getAccountType = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.type : '';
  };

  // 移除getAccountTarget函数，因为标的信息现在在记录中

  const isInvestmentAccount = (type) => {
    return ['基金', '理财产品', '股票'].includes(type);
  };

  const getTypeColor = (type) => {
    const colorMap = {
      '银行卡': 'blue',
      '基金': 'green',
      '理财产品': 'orange',
      '股票': 'red',
      '信用卡': 'purple',
      '其他': 'default'
    };
    return colorMap[type] || 'default';
  };

  const handleDelete = (recordId) => {
    deleteRecord(recordId);
    message.success('删除成功');
  };

  // 过滤数据
  const filteredRecords = useMemo(() => {
    if (!records || records.length === 0) {
      return [];
    }

    return records.filter(record => {
      try {
        // 账户过滤
        if (filters.accountId && record.accountId !== filters.accountId) {
          return false;
        }

        // 月份快速筛选
        if (filters.monthFilter) {
          const recordDate = dayjs(record.date);
          if (!recordDate.isValid()) {
            console.warn('无效的记录日期:', record.date);
            return false;
          }
          const recordMonth = recordDate.format('YYYY-MM');
          console.log('月份筛选:', { recordMonth, filterMonth: filters.monthFilter, match: recordMonth === filters.monthFilter });
          if (recordMonth !== filters.monthFilter) {
            return false;
          }
        }

        // 日期范围过滤（只有在没有月份筛选时才生效）
        if (!filters.monthFilter && filters.dateRange && Array.isArray(filters.dateRange) && filters.dateRange.length === 2) {
          const recordDate = dayjs(record.date);
          const startDate = dayjs(filters.dateRange[0]);
          const endDate = dayjs(filters.dateRange[1]);

          // 确保所有日期都有效
          if (!recordDate.isValid() || !startDate.isValid() || !endDate.isValid()) {
            console.warn('无效日期:', { record: record.date, start: filters.dateRange[0], end: filters.dateRange[1] });
            return false;
          }

          // 使用字符串比较，更稳定
          const recordDateStr = recordDate.format('YYYY-MM-DD');
          const startDateStr = startDate.format('YYYY-MM-DD');
          const endDateStr = endDate.format('YYYY-MM-DD');

          if (recordDateStr < startDateStr || recordDateStr > endDateStr) {
            return false;
          }
        }

        // 文本搜索过滤
        if (filters.searchText && filters.searchText.trim()) {
          const searchLower = filters.searchText.toLowerCase().trim();
          const accountName = getAccountName(record.accountId).toLowerCase();
          const note = (record.note || '').toLowerCase();
          const target = (record.target || '').toLowerCase();

          if (!accountName.includes(searchLower) &&
              !note.includes(searchLower) &&
              !target.includes(searchLower)) {
            return false;
          }
        }

        return true;
      } catch (error) {
        console.error('过滤记录时出错:', error, record);
        return false;
      }
    });
  }, [records, filters, accounts]);

  // 生成月份选项（基于现有记录）
  const getMonthOptions = () => {
    const months = new Set();
    records.forEach(record => {
      const month = dayjs(record.date).format('YYYY-MM');
      months.add(month);
    });

    return Array.from(months)
      .sort((a, b) => b.localeCompare(a)) // 降序排列，最新月份在前
      .map(month => ({
        value: month,
        label: dayjs(month).format('YYYY年MM月')
      }));
  };

  // 处理表格变化（分页、排序等）
  const handleTableChange = (paginationConfig, tableFilters, sorter) => {
    console.log('表格变化:', { paginationConfig, tableFilters, sorter });

    // 只处理分页变化，不处理其他变化
    if (paginationConfig.current !== pagination.current ||
        paginationConfig.pageSize !== pagination.pageSize) {
      setPagination({
        ...pagination,
        current: paginationConfig.current,
        pageSize: paginationConfig.pageSize
      });
    }
  };

  // 重置筛选条件时重置分页
  const resetFilters = () => {
    setFilters({
      accountId: null,
      dateRange: null,
      searchText: '',
      monthFilter: null
    });
    setPagination({
      ...pagination,
      current: 1
    });
  };

  const columns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      sorter: (a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf(),
      defaultSortOrder: 'descend',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '账户名称',
      key: 'accountName',
      width: 130,
      render: (_, record) => getAccountName(record.accountId),
    },
    {
      title: '账户类型',
      key: 'accountType',
      width: 90,
      render: (_, record) => {
        const type = getAccountType(record.accountId);
        return <Tag color={getTypeColor(type)}>{type}</Tag>;
      },
    },
    {
      title: '标的信息',
      key: 'target',
      width: 150,
      ellipsis: true,
      render: (_, record) => {
        const type = getAccountType(record.accountId);

        if (!isInvestmentAccount(type) || !record.target) {
          return '-';
        }

        return (
          <span title={record.target} style={{ color: '#1890ff' }}>
            {record.target}
          </span>
        );
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      sorter: (a, b) => a.amount - b.amount,
      render: (amount) => (
        <span style={{ fontWeight: 'bold' }}>
          ¥{amount.toLocaleString()}
        </span>
      ),
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这条记录吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 筛选器 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Select
            placeholder="选择账户"
            allowClear
            style={{ width: '100%' }}
            value={filters.accountId}
            onChange={(value) => {
              setFilters(prev => ({ ...prev, accountId: value }));
              setPagination(prev => ({ ...prev, current: 1 }));
            }}
          >
            {accounts.map(account => (
              <Option key={account.id} value={account.id}>
                {account.name} ({account.type})
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={6}>
          <Select
            placeholder="选择月份"
            allowClear
            style={{ width: '100%' }}
            value={filters.monthFilter}
            onChange={(value) => {
              setFilters(prev => ({
                ...prev,
                monthFilter: value,
                dateRange: null // 清空日期范围筛选
              }));
              setPagination(prev => ({ ...prev, current: 1 }));
            }}
          >
            {getMonthOptions().map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Col>
        <Col xs={24} sm={6}>
          <RangePicker
            style={{ width: '100%' }}
            value={filters.dateRange}
            disabled={!!filters.monthFilter}
            onChange={(dates) => {
              console.log('日期范围变化:', dates);
              setFilters(prev => ({ ...prev, dateRange: dates }));
              setPagination(prev => ({ ...prev, current: 1 }));
            }}
            placeholder={['开始日期', '结束日期']}
            allowClear
          />
        </Col>
        <Col xs={24} sm={6}>
          <Input
            placeholder="搜索账户、备注、标的"
            prefix={<SearchOutlined />}
            allowClear
            value={filters.searchText}
            onChange={(e) => {
              setFilters(prev => ({ ...prev, searchText: e.target.value }));
              setPagination(prev => ({ ...prev, current: 1 }));
            }}
          />
        </Col>
      </Row>

      {/* 筛选状态和重置按钮 */}
      {(filters.accountId || filters.monthFilter || filters.dateRange || filters.searchText) && (
        <Row style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Space>
              <span style={{ color: '#666' }}>
                当前筛选: {filteredRecords.length} 条记录
              </span>
              <Button size="small" onClick={resetFilters}>
                重置筛选
              </Button>
            </Space>
          </Col>
        </Row>
      )}

      {/* 表格 */}
      <Table
        key="record-table"
        columns={columns}
        dataSource={filteredRecords}
        rowKey={(record) => record.id}
        pagination={{
          ...pagination,
          total: filteredRecords.length,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        }}
        onChange={handleTableChange}
        locale={{
          emptyText: (
            <Empty
              description="暂无记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ),
        }}
        scroll={{ x: 800 }}
      />
    </div>
  );
}

export default RecordList;
