import React, { useMemo } from 'react';
import { Card, Empty, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import ReactECharts from 'echarts-for-react';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

function QuickChart() {
  const navigate = useNavigate();
  const { records, accounts, getTotalAssets } = useAsset();

  // 生成最近30天的资产趋势数据
  const chartData = useMemo(() => {
    if (records.length === 0) return null;

    const days = [];
    const values = [];
    
    // 生成最近30天的日期
    for (let i = 29; i >= 0; i--) {
      const date = dayjs().subtract(i, 'day');
      days.push(date.format('MM-DD'));
      values.push(getTotalAssets(date.toDate()));
    }

    return {
      dates: days,
      values: values
    };
  }, [records, accounts, getTotalAssets]);

  const option = {
    title: {
      text: '最近30天资产趋势',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const value = params[0].value;
        return `${params[0].axisValue}<br/>总资产: ¥${value.toLocaleString()}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData?.dates || [],
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w';
          }
          return value.toLocaleString();
        },
        fontSize: 10
      }
    },
    series: [
      {
        name: '总资产',
        type: 'line',
        data: chartData?.values || [],
        smooth: true,
        lineStyle: {
          color: '#1890ff',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.05)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 4,
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  };

  return (
    <Card 
      title="资产趋势" 
      extra={
        <Button 
          type="link" 
          onClick={() => navigate('/visualization')}
        >
          查看详情
        </Button>
      }
    >
      {!chartData || chartData.values.every(v => v === 0) ? (
        <Empty 
          description="暂无数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary" 
            onClick={() => navigate('/assets')}
          >
            添加资产记录
          </Button>
        </Empty>
      ) : (
        <ReactECharts 
          option={option} 
          style={{ height: '300px' }}
          opts={{ renderer: 'svg' }}
        />
      )}
    </Card>
  );
}

export default QuickChart;
