.data-visualization {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.data-visualization .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.data-visualization .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.data-visualization .ant-statistic-title {
  font-weight: 500;
  color: #666;
}

.data-visualization .ant-statistic-content {
  font-weight: 600;
}

.data-visualization .ant-select,
.data-visualization .ant-picker {
  border-radius: 6px;
}

.data-visualization .ant-card-head-title {
  font-weight: 600;
  color: #262626;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-visualization {
    padding: 16px;
  }
  
  .data-visualization .ant-col {
    margin-bottom: 16px;
  }
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 400px;
}

/* 统计卡片动画 */
.data-visualization .ant-statistic {
  text-align: center;
}

.data-visualization .ant-statistic-content-value {
  font-size: 24px;
}

/* 筛选器样式 */
.filter-section {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}
