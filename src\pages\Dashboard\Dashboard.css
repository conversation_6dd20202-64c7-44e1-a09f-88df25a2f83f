.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 24px;
}

.statistics-row {
  margin-bottom: 24px;
}

.statistic-card {
  position: relative;
  overflow: hidden;
}

.statistic-card .ant-statistic {
  position: relative;
  z-index: 2;
}

.statistic-icon {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 32px;
  opacity: 0.3;
  z-index: 1;
}

.quick-actions-card {
  margin-bottom: 24px;
}

.action-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 24px;
  color: #1890ff;
  margin-bottom: 8px;
}

.action-card .ant-typography {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }
  
  .statistic-card {
    margin-bottom: 16px;
  }
  
  .action-card {
    height: 100px;
  }
  
  .action-icon {
    font-size: 20px;
  }
}
