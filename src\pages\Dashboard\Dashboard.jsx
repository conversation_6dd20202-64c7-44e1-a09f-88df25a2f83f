import React from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd';
import {
  BankOutlined,
  TrendingUpOutlined,
  PlusOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAsset } from '../../context/AssetContext';
import './Dashboard.css';

const { Title, Text } = Typography;

function Dashboard() {
  const navigate = useNavigate();
  const { accounts, records, getTotalAssets } = useAsset();

  // 计算统计数据
  const totalAssets = getTotalAssets();
  const totalAccounts = accounts.length;
  const totalRecords = records.length;

  // 计算本月变化
  const currentMonth = new Date();
  const lastMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
  const lastMonthAssets = getTotalAssets(lastMonth);
  const monthlyChange = totalAssets - lastMonthAssets;
  const monthlyChangePercent = lastMonthAssets > 0 ? ((monthlyChange / lastMonthAssets) * 100) : 0;

  const statisticCards = [
    {
      title: '总资产',
      value: totalAssets,
      precision: 2,
      prefix: '¥',
      icon: <BankOutlined style={{ color: '#1890ff' }} />,
      color: '#1890ff'
    },
    {
      title: '账户数量',
      value: totalAccounts,
      icon: <BankOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a'
    },
    {
      title: '记录总数',
      value: totalRecords,
      icon: <TrendingUpOutlined style={{ color: '#722ed1' }} />,
      color: '#722ed1'
    },
    {
      title: '本月变化',
      value: monthlyChange,
      precision: 2,
      prefix: monthlyChange >= 0 ? '+¥' : '-¥',
      suffix: `(${monthlyChangePercent.toFixed(1)}%)`,
      valueStyle: { color: monthlyChange >= 0 ? '#3f8600' : '#cf1322' },
      icon: <TrendingUpOutlined style={{ color: monthlyChange >= 0 ? '#3f8600' : '#cf1322' }} />
    }
  ];

  const quickActions = [
    {
      title: '添加记录',
      description: '快速录入资产余额',
      icon: <PlusOutlined />,
      action: () => navigate('/assets'),
      type: 'primary'
    },
    {
      title: '查看图表',
      description: '查看资产趋势图表',
      icon: <EyeOutlined />,
      action: () => navigate('/visualization'),
      type: 'default'
    }
  ];

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <Title level={2}>总览</Title>
        <Text type="secondary">欢迎使用家财管家，管理您的家庭财务</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="statistics-row">
        {statisticCards.map((card, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card className="statistic-card">
              <Statistic
                title={card.title}
                value={card.value}
                precision={card.precision}
                prefix={card.prefix}
                suffix={card.suffix}
                valueStyle={card.valueStyle}
              />
              <div className="statistic-icon">
                {card.icon}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" className="quick-actions-card">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} key={index}>
              <Card
                hoverable
                className="action-card"
                onClick={action.action}
              >
                <Space direction="vertical" size="small">
                  <div className="action-icon">
                    {action.icon}
                  </div>
                  <Title level={4}>{action.title}</Title>
                  <Text type="secondary">{action.description}</Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 内容区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="资产趋势">
            <p>图表组件开发中...</p>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="最近记录">
            <p>记录组件开发中...</p>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default Dashboard;
