import React from 'react';
import { Card, List, Typography, Tag, Empty, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Text, Title } = Typography;

function RecentRecords() {
  const navigate = useNavigate();
  const { records, accounts } = useAsset();

  // 获取最近的10条记录
  const recentRecords = records
    .sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf())
    .slice(0, 10);

  const getAccountName = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.name : '未知账户';
  };

  const getAccountType = (accountId) => {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.type : '';
  };

  const getTypeColor = (type) => {
    const colorMap = {
      '银行卡': 'blue',
      '基金': 'green',
      '理财产品': 'orange',
      '股票': 'red',
      '信用卡': 'purple',
      '其他': 'default'
    };
    return colorMap[type] || 'default';
  };

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Card 
      title="最近记录" 
      extra={
        <Button 
          type="link" 
          onClick={() => navigate('/assets')}
        >
          查看全部
        </Button>
      }
    >
      {recentRecords.length === 0 ? (
        <Empty 
          description="暂无记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button 
            type="primary" 
            onClick={() => navigate('/assets')}
          >
            添加第一条记录
          </Button>
        </Empty>
      ) : (
        <List
          dataSource={recentRecords}
          renderItem={(record) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span>{getAccountName(record.accountId)}</span>
                    <Tag color={getTypeColor(getAccountType(record.accountId))}>
                      {getAccountType(record.accountId)}
                    </Tag>
                  </div>
                }
                description={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text type="secondary">
                      {dayjs(record.date).format('YYYY-MM-DD')}
                    </Text>
                    <Text strong style={{ fontSize: '16px' }}>
                      {formatAmount(record.amount)}
                    </Text>
                  </div>
                }
              />
              {record.note && (
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    备注: {record.note}
                  </Text>
                </div>
              )}
            </List.Item>
          )}
        />
      )}
    </Card>
  );
}

export default RecentRecords;
