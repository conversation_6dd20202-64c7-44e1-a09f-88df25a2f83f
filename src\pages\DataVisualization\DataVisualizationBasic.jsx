import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Statistic,
  Space,
  Progress,
  Tag,
  Alert
} from 'antd';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

function DataVisualizationBasic() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6);

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 获取账户类型列表
  const accountTypes = useMemo(() => {
    try {
      const types = new Set(safeAccounts.map(acc => acc.type));
      return Array.from(types);
    } catch (error) {
      console.error('获取账户类型错误:', error);
      return [];
    }
  }, [safeAccounts]);

  // 计算每个账户的当前余额
  const getAccountBalance = (accountId) => {
    try {
      const accountRecords = safeRecords.filter(record => record.accountId === accountId);
      return accountRecords.reduce((balance, record) => balance + (record.amount || 0), 0);
    } catch (error) {
      console.error('计算账户余额错误:', error);
      return 0;
    }
  };

  // 计算当前资产统计
  const currentStats = useMemo(() => {
    try {
      let totalAssets = 0;
      const typeBalances = {};

      safeAccounts.forEach(account => {
        const balance = getAccountBalance(account.id);
        totalAssets += balance;
        
        if (!typeBalances[account.type]) {
          typeBalances[account.type] = 0;
        }
        typeBalances[account.type] += balance;
      });

      return {
        total: totalAssets,
        byType: typeBalances,
        accountCount: safeAccounts.length,
        recordCount: safeRecords.length
      };
    } catch (error) {
      console.error('计算统计数据错误:', error);
      return {
        total: 0,
        byType: {},
        accountCount: 0,
        recordCount: 0
      };
    }
  }, [safeAccounts, safeRecords]);

  // 生成月度数据（简化版）
  const monthlyData = useMemo(() => {
    try {
      const months = [];
      for (let i = selectedMonths - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
      }
      return months;
    } catch (error) {
      console.error('生成月度数据错误:', error);
      return [];
    }
  }, [selectedMonths]);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产数据分析</Title>
      
      <Alert
        message="数据可视化功能"
        description="当前显示基础统计信息，图表功能正在优化中。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 月份筛选器 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Select
            value={selectedMonths}
            onChange={setSelectedMonths}
            style={{ width: '100%' }}
            placeholder="选择显示月份数"
          >
            <Option value={3}>最近3个月</Option>
            <Option value={6}>最近6个月</Option>
            <Option value={12}>最近12个月</Option>
            <Option value={24}>最近24个月</Option>
          </Select>
        </Col>
        <Col span={16}>
          <Space>
            <span style={{ color: '#666' }}>
              数据范围: {monthlyData.length > 0 ? 
                `${dayjs(monthlyData[0]).format('YYYY年MM月')} 至 ${dayjs(monthlyData[monthlyData.length - 1]).format('YYYY年MM月')}` : 
                '暂无数据'}
            </span>
          </Space>
        </Col>
      </Row>

      {/* 当前资产概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title={`总资产 (${dayjs().format('MM月')})`}
              value={currentStats.total}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
            />
            <div style={{ marginTop: 8 }}>
              <Tag color="blue">
                当前月份
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="主要资产类别">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 3)
                .map(([type, amount]) => (
                  <div key={type} style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{type}:</span>
                    <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                  </div>
                ))}
              {Object.keys(currentStats.byType).length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="资产占比">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType)
                .sort(([,a], [,b]) => b - a)
                .map(([type, amount]) => {
                  const percentage = currentStats.total > 0 ? (amount / currentStats.total * 100) : 0;
                  return (
                    <div key={type}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                        <span>{type}</span>
                        <span>{percentage.toFixed(1)}%</span>
                      </div>
                      <Progress percent={percentage} size="small" showInfo={false} />
                    </div>
                  );
                })}
              {Object.keys(currentStats.byType).length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 基础统计信息 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="账户类型分布">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType).map(([type, amount]) => (
                <div key={type} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <span>{type}</span>
                  <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                </div>
              ))}
              {Object.keys(currentStats.byType).length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="系统统计">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="账户数量" value={currentStats.accountCount} suffix="个" />
              <Statistic title="记录数量" value={currentStats.recordCount} suffix="条" />
              <Statistic title="资产类型" value={accountTypes.length} suffix="种" />
              <div style={{ marginTop: 16, color: '#666' }}>
                <p>数据更新时间: {dayjs().format('YYYY-MM-DD HH:mm')}</p>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 功能说明 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="功能说明">
            <div style={{ color: '#666' }}>
              <p><strong>当前功能：</strong></p>
              <ul>
                <li>✅ 总资产统计</li>
                <li>✅ 资产类型分布</li>
                <li>✅ 资产占比分析</li>
                <li>✅ 基础统计信息</li>
              </ul>
              <p><strong>即将推出：</strong></p>
              <ul>
                <li>📊 资产变动趋势图</li>
                <li>📈 环比变动分析</li>
                <li>🥧 交互式饼图</li>
                <li>📅 月度数据对比</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualizationBasic;
