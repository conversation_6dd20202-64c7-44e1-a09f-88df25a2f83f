import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Statistic,
  Space,
  Progress,
  Tag,
  Alert,
  Button,
  Modal,
  Table,
  Divider
} from 'antd';
import { EyeOutlined, CalendarOutlined } from '@ant-design/icons';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

function DataVisualizationBasic() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6); // 图表时间范围
  const [currentAssetMonth, setCurrentAssetMonth] = useState(dayjs().format('YYYY-MM')); // 当前资产汇总月份
  const [detailModalVisible, setDetailModalVisible] = useState(false); // 明细弹窗显示状态

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 获取账户类型列表
  const accountTypes = useMemo(() => {
    try {
      const types = new Set(safeAccounts.map(acc => acc.type));
      return Array.from(types);
    } catch (error) {
      console.error('获取账户类型错误:', error);
      return [];
    }
  }, [safeAccounts]);

  // 生成可选月份列表（最近24个月）
  const availableMonths = useMemo(() => {
    const months = [];
    for (let i = 0; i < 24; i++) {
      const month = dayjs().subtract(i, 'month');
      months.push({
        value: month.format('YYYY-MM'),
        label: month.format('YYYY年MM月')
      });
    }
    return months;
  }, []);

  // 计算每个账户在指定月份末的余额
  const getAccountBalanceAtMonth = (accountId, month) => {
    try {
      const monthEnd = dayjs(month).endOf('month');
      const accountRecords = safeRecords
        .filter(record => record.accountId === accountId)
        .filter(record => dayjs(record.date).isSameOrBefore(monthEnd, 'day'));
      return accountRecords.reduce((balance, record) => balance + (record.amount || 0), 0);
    } catch (error) {
      console.error('计算账户余额错误:', error);
      return 0;
    }
  };

  // 计算指定月份的资产统计
  const currentStats = useMemo(() => {
    try {
      let totalAssets = 0;
      const typeBalances = {};
      const accountDetails = [];

      safeAccounts.forEach(account => {
        const balance = getAccountBalanceAtMonth(account.id, currentAssetMonth);
        totalAssets += balance;

        if (!typeBalances[account.type]) {
          typeBalances[account.type] = 0;
        }
        typeBalances[account.type] += balance;

        // 收集账户明细
        accountDetails.push({
          id: account.id,
          name: account.name,
          type: account.type,
          balance: balance,
          target: account.target || '-'
        });
      });

      return {
        total: totalAssets,
        byType: typeBalances,
        accountDetails: accountDetails.filter(acc => acc.balance !== 0), // 只显示有余额的账户
        accountCount: safeAccounts.length,
        recordCount: safeRecords.length,
        month: currentAssetMonth
      };
    } catch (error) {
      console.error('计算统计数据错误:', error);
      return {
        total: 0,
        byType: {},
        accountDetails: [],
        accountCount: 0,
        recordCount: 0,
        month: currentAssetMonth
      };
    }
  }, [safeAccounts, safeRecords, currentAssetMonth, getAccountBalanceAtMonth]);

  // 生成月度数据（简化版）
  const monthlyData = useMemo(() => {
    try {
      const months = [];
      for (let i = selectedMonths - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
      }
      return months;
    } catch (error) {
      console.error('生成月度数据错误:', error);
      return [];
    }
  }, [selectedMonths]);

  // 明细表格列定义
  const detailColumns = [
    {
      title: '账户名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '账户类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '标的/目标',
      dataIndex: 'target',
      key: 'target',
    },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance) => (
        <span style={{
          color: balance >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          ¥{balance.toLocaleString()}
        </span>
      ),
      sorter: (a, b) => a.balance - b.balance,
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_, record) => {
        const percentage = currentStats.total > 0 ? (record.balance / currentStats.total * 100) : 0;
        return `${percentage.toFixed(2)}%`;
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产数据分析</Title>

      <Alert
        message="数据可视化功能"
        description="当前显示基础统计信息，图表功能正在优化中。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 筛选器区域 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card size="small" title="当前资产汇总设置">
            <Space>
              <CalendarOutlined />
              <span>汇总月份:</span>
              <Select
                value={currentAssetMonth}
                onChange={setCurrentAssetMonth}
                style={{ width: 150 }}
                placeholder="选择月份"
              >
                {availableMonths.map(month => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card size="small" title="图表时间范围设置">
            <Space>
              <span>显示范围:</span>
              <Select
                value={selectedMonths}
                onChange={setSelectedMonths}
                style={{ width: 150 }}
                placeholder="选择显示月份数"
              >
                <Option value={3}>最近3个月</Option>
                <Option value={6}>最近6个月</Option>
                <Option value={12}>最近12个月</Option>
                <Option value={24}>最近24个月</Option>
              </Select>
              <span style={{ color: '#666', fontSize: '12px' }}>
                ({monthlyData.length > 0 ?
                  `${dayjs(monthlyData[0]).format('MM月')} 至 ${dayjs(monthlyData[monthlyData.length - 1]).format('MM月')}` :
                  '暂无数据'})
              </span>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 当前资产概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card>
            <Statistic
              title={`总资产 (${dayjs(currentAssetMonth).format('YYYY年MM月')})`}
              value={currentStats.total}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
            />
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                icon={<EyeOutlined />}
                onClick={() => setDetailModalVisible(true)}
                disabled={currentStats.accountDetails.length === 0}
              >
                查看明细
              </Button>
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {currentStats.accountDetails.length} 个有余额账户
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="资产类型分布">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType)
                .sort(([,a], [,b]) => b - a)
                .map(([type, amount]) => {
                  const percentage = currentStats.total > 0 ? (amount / currentStats.total * 100) : 0;
                  return (
                    <div key={type}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                        <span>{type}</span>
                        <Space>
                          <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                          <span style={{ color: '#666' }}>({percentage.toFixed(1)}%)</span>
                        </Space>
                      </div>
                      <Progress
                        percent={percentage}
                        size="small"
                        showInfo={false}
                        strokeColor={percentage > 50 ? '#52c41a' : percentage > 20 ? '#1890ff' : '#faad14'}
                      />
                    </div>
                  );
                })}
              {Object.keys(currentStats.byType).length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 基础统计信息 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="账户类型分布">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType).map(([type, amount]) => (
                <div key={type} style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: '1px solid #f0f0f0'
                }}>
                  <span>{type}</span>
                  <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                </div>
              ))}
              {Object.keys(currentStats.byType).length === 0 && (
                <span style={{ color: '#999' }}>暂无数据</span>
              )}
            </Space>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="系统统计">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="账户数量" value={currentStats.accountCount} suffix="个" />
              <Statistic title="记录数量" value={currentStats.recordCount} suffix="条" />
              <Statistic title="资产类型" value={accountTypes.length} suffix="种" />
              <div style={{ marginTop: 16, color: '#666' }}>
                <p>数据更新时间: {dayjs().format('YYYY-MM-DD HH:mm')}</p>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 功能说明 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="功能说明">
            <div style={{ color: '#666' }}>
              <p><strong>当前功能：</strong></p>
              <ul>
                <li>✅ 总资产统计（支持选择汇总月份）</li>
                <li>✅ 资产类型分布（合并显示金额和占比）</li>
                <li>✅ 资产明细查看（弹窗显示详细信息）</li>
                <li>✅ 分离的时间筛选器（图表和资产汇总独立设置）</li>
              </ul>
              <p><strong>即将推出：</strong></p>
              <ul>
                <li>📊 资产变动趋势图（使用图表时间范围）</li>
                <li>📈 环比变动分析</li>
                <li>🥧 交互式饼图</li>
                <li>📅 月度数据对比</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 资产明细弹窗 */}
      <Modal
        title={`资产明细 - ${dayjs(currentStats.month).format('YYYY年MM月')}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <Statistic
            title="总资产"
            value={currentStats.total}
            precision={2}
            prefix="¥"
            valueStyle={{ color: '#1890ff', fontSize: '24px' }}
          />
        </div>
        <Divider />
        <Table
          columns={detailColumns}
          dataSource={currentStats.accountDetails}
          rowKey="id"
          pagination={false}
          size="small"
          summary={(pageData) => {
            const total = pageData.reduce((sum, record) => sum + record.balance, 0);
            return (
              <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                <Table.Summary.Cell colSpan={3}>
                  <strong>合计</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell>
                  <strong style={{ color: '#1890ff' }}>
                    ¥{total.toLocaleString()}
                  </strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell>
                  <strong>100.00%</strong>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            );
          }}
        />
      </Modal>
    </div>
  );
}

export default DataVisualizationBasic;
