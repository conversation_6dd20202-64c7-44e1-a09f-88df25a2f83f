import React, { createContext, useContext, useReducer, useEffect } from 'react';
import dayjs from 'dayjs';

// 生成唯一ID的函数
let idCounter = 0;
const generateUniqueId = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000); // 添加随机数
  idCounter = (idCounter + 1) % 10000; // 增大计数器范围
  return `${timestamp}_${random}_${idCounter}`;
};

// 清理重复ID的函数
const cleanupDuplicateIds = (data) => {
  const { accounts = [], records = [] } = data;
  const usedIds = new Set();

  // 清理账户ID
  const cleanAccounts = accounts.map(account => {
    if (!account.id || usedIds.has(account.id)) {
      const newId = generateUniqueId();
      usedIds.add(newId);
      return { ...account, id: newId };
    }
    usedIds.add(account.id);
    return account;
  });

  // 清理记录ID
  const cleanRecords = records.map(record => {
    if (!record.id || usedIds.has(record.id)) {
      const newId = generateUniqueId();
      usedIds.add(newId);
      return { ...record, id: newId };
    }
    usedIds.add(record.id);
    return record;
  });

  return { accounts: cleanAccounts, records: cleanRecords };
};

// 初始状态
const initialState = {
  accounts: [],
  records: [],
  loading: false,
  error: null
};

// Action 类型
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  ADD_ACCOUNT: 'ADD_ACCOUNT',
  UPDATE_ACCOUNT: 'UPDATE_ACCOUNT',
  DELETE_ACCOUNT: 'DELETE_ACCOUNT',
  ADD_RECORD: 'ADD_RECORD',
  UPDATE_RECORD: 'UPDATE_RECORD',
  DELETE_RECORD: 'DELETE_RECORD',
  LOAD_DATA: 'LOAD_DATA'
};

// Reducer
function assetReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };

    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload };

    case ActionTypes.ADD_ACCOUNT:
      return {
        ...state,
        accounts: [...state.accounts, { ...action.payload, id: generateUniqueId() }]
      };

    case ActionTypes.UPDATE_ACCOUNT:
      return {
        ...state,
        accounts: state.accounts.map(account =>
          account.id === action.payload.id ? action.payload : account
        )
      };

    case ActionTypes.DELETE_ACCOUNT:
      return {
        ...state,
        accounts: state.accounts.filter(account => account.id !== action.payload),
        records: state.records.filter(record => record.accountId !== action.payload)
      };

    case ActionTypes.ADD_RECORD:
      return {
        ...state,
        records: [...state.records, { ...action.payload, id: generateUniqueId() }]
      };

    case ActionTypes.UPDATE_RECORD:
      return {
        ...state,
        records: state.records.map(record =>
          record.id === action.payload.id ? action.payload : record
        )
      };

    case ActionTypes.DELETE_RECORD:
      return {
        ...state,
        records: state.records.filter(record => record.id !== action.payload)
      };

    case ActionTypes.LOAD_DATA:
      return {
        ...state,
        accounts: action.payload.accounts || [],
        records: action.payload.records || []
      };

    default:
      return state;
  }
}

// Context
const AssetContext = createContext();

// Provider 组件
export function AssetProvider({ children }) {
  const [state, dispatch] = useReducer(assetReducer, initialState);
  const [isLoaded, setIsLoaded] = React.useState(false);

  // 从 localStorage 加载数据
  useEffect(() => {
    console.log('AssetContext: 开始加载数据...');
    try {
      const savedData = localStorage.getItem('familyFinanceData');
      console.log('AssetContext: localStorage数据:', savedData);

      if (savedData) {
        const data = JSON.parse(savedData);
        console.log('AssetContext: 解析后的数据:', data);

        // 清理重复ID
        const cleanData = cleanupDuplicateIds(data);
        console.log('AssetContext: 数据清理完成:', cleanData);

        // 保存清理后的数据
        localStorage.setItem('familyFinanceData', JSON.stringify(cleanData));
        dispatch({ type: ActionTypes.LOAD_DATA, payload: cleanData });
        console.log('AssetContext: 数据已加载到state');
      } else {
        console.log('AssetContext: 没有找到保存的数据');
      }
      setIsLoaded(true);
    } catch (error) {
      console.error('AssetContext: 加载数据失败:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: '加载数据失败' });
      setIsLoaded(true);
    }
  }, []);

  // 保存数据到 localStorage (只在数据加载完成后)
  useEffect(() => {
    if (!isLoaded) return; // 避免在初始加载时保存空数据

    try {
      const dataToSave = {
        accounts: state.accounts,
        records: state.records
      };
      localStorage.setItem('familyFinanceData', JSON.stringify(dataToSave));
      console.log('数据已保存到localStorage:', dataToSave);
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }, [state.accounts, state.records, isLoaded]);

  // Actions
  const actions = {
    addAccount: (account) => {
      dispatch({ type: ActionTypes.ADD_ACCOUNT, payload: account });
    },

    updateAccount: (account) => {
      dispatch({ type: ActionTypes.UPDATE_ACCOUNT, payload: account });
    },

    deleteAccount: (accountId) => {
      dispatch({ type: ActionTypes.DELETE_ACCOUNT, payload: accountId });
    },

    addRecord: (record) => {
      dispatch({ type: ActionTypes.ADD_RECORD, payload: record });
    },

    updateRecord: (record) => {
      dispatch({ type: ActionTypes.UPDATE_RECORD, payload: record });
    },

    deleteRecord: (recordId) => {
      dispatch({ type: ActionTypes.DELETE_RECORD, payload: recordId });
    },

    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },

    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    }
  };

  // 计算总资产 (简化版本)
  const getTotalAssets = () => {
    let total = 0;

    state.accounts.forEach(account => {
      const accountRecords = state.records
        .filter(record => record.accountId === account.id)
        .sort((a, b) => new Date(b.date) - new Date(a.date));

      if (accountRecords.length > 0) {
        total += accountRecords[0].amount;
      }
    });

    return total;
  };

  // 获取账户历史数据
  const getAccountHistory = (accountId, startDate, endDate) => {
    return state.records
      .filter(record => {
        const recordDate = dayjs(record.date);
        return record.accountId === accountId &&
               recordDate.isAfter(dayjs(startDate).subtract(1, 'day')) &&
               recordDate.isBefore(dayjs(endDate).add(1, 'day'));
      })
      .sort((a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf());
  };

  // 调试当前state
  console.log('AssetContext: 当前state:', {
    accounts: state.accounts,
    records: state.records,
    accountsLength: state.accounts.length,
    recordsLength: state.records.length,
    isLoaded
  });

  const value = {
    ...state,
    ...actions,
    getTotalAssets,
    getAccountHistory
  };

  return (
    <AssetContext.Provider value={value}>
      {children}
    </AssetContext.Provider>
  );
}

// Hook
export function useAsset() {
  const context = useContext(AssetContext);
  if (!context) {
    throw new Error('useAsset must be used within an AssetProvider');
  }
  return context;
}
