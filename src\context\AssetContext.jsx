import React, { createContext, useContext, useReducer, useEffect } from 'react';
import dayjs from 'dayjs';

// 初始状态
const initialState = {
  accounts: [],
  records: [],
  loading: false,
  error: null
};

// Action 类型
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  ADD_ACCOUNT: 'ADD_ACCOUNT',
  UPDATE_ACCOUNT: 'UPDATE_ACCOUNT',
  DELETE_ACCOUNT: 'DELETE_ACCOUNT',
  ADD_RECORD: 'ADD_RECORD',
  UPDATE_RECORD: 'UPDATE_RECORD',
  DELETE_RECORD: 'DELETE_RECORD',
  LOAD_DATA: 'LOAD_DATA'
};

// Reducer
function assetReducer(state, action) {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
    
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload };
    
    case ActionTypes.ADD_ACCOUNT:
      return {
        ...state,
        accounts: [...state.accounts, { ...action.payload, id: Date.now().toString() }]
      };
    
    case ActionTypes.UPDATE_ACCOUNT:
      return {
        ...state,
        accounts: state.accounts.map(account =>
          account.id === action.payload.id ? action.payload : account
        )
      };
    
    case ActionTypes.DELETE_ACCOUNT:
      return {
        ...state,
        accounts: state.accounts.filter(account => account.id !== action.payload),
        records: state.records.filter(record => record.accountId !== action.payload)
      };
    
    case ActionTypes.ADD_RECORD:
      return {
        ...state,
        records: [...state.records, { ...action.payload, id: Date.now().toString() }]
      };
    
    case ActionTypes.UPDATE_RECORD:
      return {
        ...state,
        records: state.records.map(record =>
          record.id === action.payload.id ? action.payload : record
        )
      };
    
    case ActionTypes.DELETE_RECORD:
      return {
        ...state,
        records: state.records.filter(record => record.id !== action.payload)
      };
    
    case ActionTypes.LOAD_DATA:
      return {
        ...state,
        accounts: action.payload.accounts || [],
        records: action.payload.records || []
      };
    
    default:
      return state;
  }
}

// Context
const AssetContext = createContext();

// Provider 组件
export function AssetProvider({ children }) {
  const [state, dispatch] = useReducer(assetReducer, initialState);

  // 从 localStorage 加载数据
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('familyFinanceData');
      if (savedData) {
        const data = JSON.parse(savedData);
        dispatch({ type: ActionTypes.LOAD_DATA, payload: data });
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      dispatch({ type: ActionTypes.SET_ERROR, payload: '加载数据失败' });
    }
  }, []);

  // 保存数据到 localStorage
  useEffect(() => {
    try {
      const dataToSave = {
        accounts: state.accounts,
        records: state.records
      };
      localStorage.setItem('familyFinanceData', JSON.stringify(dataToSave));
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  }, [state.accounts, state.records]);

  // Actions
  const actions = {
    addAccount: (account) => {
      dispatch({ type: ActionTypes.ADD_ACCOUNT, payload: account });
    },
    
    updateAccount: (account) => {
      dispatch({ type: ActionTypes.UPDATE_ACCOUNT, payload: account });
    },
    
    deleteAccount: (accountId) => {
      dispatch({ type: ActionTypes.DELETE_ACCOUNT, payload: accountId });
    },
    
    addRecord: (record) => {
      dispatch({ type: ActionTypes.ADD_RECORD, payload: record });
    },
    
    updateRecord: (record) => {
      dispatch({ type: ActionTypes.UPDATE_RECORD, payload: record });
    },
    
    deleteRecord: (recordId) => {
      dispatch({ type: ActionTypes.DELETE_RECORD, payload: recordId });
    },
    
    setLoading: (loading) => {
      dispatch({ type: ActionTypes.SET_LOADING, payload: loading });
    },
    
    setError: (error) => {
      dispatch({ type: ActionTypes.SET_ERROR, payload: error });
    }
  };

  // 计算总资产
  const getTotalAssets = (date = null) => {
    const targetDate = date ? dayjs(date) : dayjs();
    let total = 0;
    
    state.accounts.forEach(account => {
      const latestRecord = state.records
        .filter(record => 
          record.accountId === account.id && 
          dayjs(record.date).isSameOrBefore(targetDate)
        )
        .sort((a, b) => dayjs(b.date).valueOf() - dayjs(a.date).valueOf())[0];
      
      if (latestRecord) {
        total += latestRecord.amount;
      }
    });
    
    return total;
  };

  // 获取账户历史数据
  const getAccountHistory = (accountId, startDate, endDate) => {
    return state.records
      .filter(record => {
        const recordDate = dayjs(record.date);
        return record.accountId === accountId &&
               recordDate.isAfter(dayjs(startDate).subtract(1, 'day')) &&
               recordDate.isBefore(dayjs(endDate).add(1, 'day'));
      })
      .sort((a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf());
  };

  const value = {
    ...state,
    ...actions,
    getTotalAssets,
    getAccountHistory
  };

  return (
    <AssetContext.Provider value={value}>
      {children}
    </AssetContext.Provider>
  );
}

// Hook
export function useAsset() {
  const context = useContext(AssetContext);
  if (!context) {
    throw new Error('useAsset must be used within an AssetProvider');
  }
  return context;
}
