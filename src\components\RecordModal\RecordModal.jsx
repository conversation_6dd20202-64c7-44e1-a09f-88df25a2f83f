import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, DatePicker, InputNumber, message } from 'antd';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Option } = Select;

function RecordModal({ visible, onCancel, record }) {
  const [form] = Form.useForm();
  const { accounts, addRecord, updateRecord } = useAsset();

  useEffect(() => {
    if (visible) {
      if (record) {
        form.setFieldsValue({
          ...record,
          date: dayjs(record.date)
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          date: dayjs()
        });
      }
    }
  }, [visible, record, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formattedValues = {
        ...values,
        date: values.date.format('YYYY-MM-DD')
      };

      if (record) {
        updateRecord({ ...record, ...formattedValues });
        message.success('记录更新成功');
      } else {
        addRecord(formattedValues);
        message.success('记录添加成功');
      }

      onCancel();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={record ? '编辑记录' : '添加记录'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      okText="确定"
      cancelText="取消"
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="accountId"
          label="选择账户"
          rules={[{ required: true, message: '请选择账户' }]}
        >
          <Select placeholder="请选择账户">
            {accounts.map(account => (
              <Option key={account.id} value={account.id}>
                {account.name} ({account.type})
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="date"
          label="日期"
          rules={[{ required: true, message: '请选择日期' }]}
        >
          <DatePicker
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
            placeholder="请选择日期"
          />
        </Form.Item>

        <Form.Item
          name="amount"
          label="金额"
          rules={[
            { required: true, message: '请输入金额' },
            { type: 'number', min: 0, message: '金额不能为负数' }
          ]}
        >
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入金额"
            precision={2}
            min={0}
            formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value.replace(/¥\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item
          name="note"
          label="备注"
          rules={[{ max: 200, message: '备注不能超过200个字符' }]}
        >
          <Input.TextArea
            rows={3}
            placeholder="请输入备注信息（可选）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default RecordModal;
