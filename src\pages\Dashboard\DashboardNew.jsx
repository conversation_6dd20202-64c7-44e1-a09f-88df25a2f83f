import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Statistic,
  Space,
  Progress,
  Tag,
  Button,
  Modal,
  Table,
  Divider,
  Alert,
  Empty
} from 'antd';
import { EyeOutlined, CalendarOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

function Dashboard() {
  const { accounts, records, addAccount, addRecord } = useAsset();
  const [currentAssetMonth, setCurrentAssetMonth] = useState('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 调试信息（可以在生产环境中移除）
  console.log('Dashboard数据状态:', {
    accounts: safeAccounts,
    records: safeRecords,
    accountsLength: safeAccounts.length,
    recordsLength: safeRecords.length,
    localStorage: localStorage.getItem('familyFinanceData')
  });

  // 添加测试数据的函数（仅用于调试）
  const addTestData = () => {
    console.log('开始创建测试数据...');

    // 清空现有数据
    localStorage.removeItem('familyFinanceData');

    // 创建测试数据
    const testData = {
      accounts: [
        {
          id: 'test_account_1',
          name: '测试银行卡',
          type: '银行卡',
          target: '工商银行'
        },
        {
          id: 'test_account_2',
          name: '测试基金',
          type: '基金',
          target: '沪深300'
        },
        {
          id: 'test_account_3',
          name: '测试信用卡',
          type: '信用卡',
          target: '招商银行'
        }
      ],
      records: [
        {
          id: 'test_record_1',
          accountId: 'test_account_1',
          amount: 10000,
          date: dayjs().format('YYYY-MM-DD'),
          note: '银行卡初始余额'
        },
        {
          id: 'test_record_2',
          accountId: 'test_account_2',
          amount: 5000,
          date: dayjs().format('YYYY-MM-DD'),
          target: '沪深300ETF',
          note: '基金投资'
        },
        {
          id: 'test_record_3',
          accountId: 'test_account_3',
          amount: -2000,
          date: dayjs().format('YYYY-MM-DD'),
          note: '信用卡负债'
        },
        {
          id: 'test_record_4',
          accountId: 'test_account_1',
          amount: 500,
          date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          note: '银行卡存款'
        },
        {
          id: 'test_record_5',
          accountId: 'test_account_2',
          amount: 1000,
          date: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
          target: '沪深300ETF',
          note: '上月基金投资'
        }
      ]
    };

    console.log('测试数据:', testData);

    // 保存到localStorage
    try {
      localStorage.setItem('familyFinanceData', JSON.stringify(testData));
      console.log('数据已保存到localStorage');

      // 验证保存是否成功
      const saved = localStorage.getItem('familyFinanceData');
      console.log('验证保存的数据:', JSON.parse(saved));

      // 刷新页面以加载新数据
      window.location.reload();
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  };

  // 清除所有数据的函数（仅用于调试）
  const clearAllData = () => {
    localStorage.removeItem('familyFinanceData');
    window.location.reload();
  };

  // 使用Context API添加测试数据（不刷新页面）
  const addTestDataViaContext = () => {
    console.log('通过Context API添加测试数据...');

    // 清空现有数据
    localStorage.removeItem('familyFinanceData');

    // 添加测试账户
    const testAccounts = [
      { name: '测试银行卡', type: '银行卡', target: '工商银行' },
      { name: '测试基金', type: '基金', target: '沪深300' },
      { name: '测试信用卡', type: '信用卡', target: '招商银行' }
    ];

    testAccounts.forEach(account => {
      addAccount(account);
    });

    // 等待账户添加完成后添加记录
    setTimeout(() => {
      console.log('当前账户列表:', safeAccounts);

      // 为每个账户添加记录
      if (safeAccounts.length >= 3) {
        const testRecords = [
          {
            accountId: safeAccounts[0].id,
            amount: 10000,
            date: dayjs().format('YYYY-MM-DD'),
            note: '银行卡初始余额'
          },
          {
            accountId: safeAccounts[1].id,
            amount: 5000,
            date: dayjs().format('YYYY-MM-DD'),
            target: '沪深300ETF',
            note: '基金投资'
          },
          {
            accountId: safeAccounts[2].id,
            amount: -2000,
            date: dayjs().format('YYYY-MM-DD'),
            note: '信用卡负债'
          }
        ];

        testRecords.forEach(record => {
          addRecord(record);
        });

        console.log('测试数据添加完成');
      }
    }, 500);
  };

  // 获取有数据的月份列表
  const availableMonths = useMemo(() => {
    if (!safeRecords.length) return [];

    const monthSet = new Set();
    safeRecords.forEach(record => {
      if (record.date) {
        const month = dayjs(record.date).format('YYYY-MM');
        monthSet.add(month);
      }
    });

    const months = Array.from(monthSet)
      .sort((a, b) => b.localeCompare(a))
      .map(month => ({
        value: month,
        label: dayjs(month).format('YYYY年MM月')
      }));

    return months;
  }, [safeRecords]);

  // 设置默认月份
  React.useEffect(() => {
    if (availableMonths.length > 0 && !currentAssetMonth) {
      setCurrentAssetMonth(availableMonths[0].value);
    }
  }, [availableMonths, currentAssetMonth]);

  // 计算账户余额
  const getAccountBalanceAtMonth = (accountId, month) => {
    try {
      if (!month) return 0;
      const monthEnd = dayjs(month).endOf('month');
      const accountRecords = safeRecords
        .filter(record => record.accountId === accountId)
        .filter(record => dayjs(record.date).isSameOrBefore(monthEnd, 'day'));
      return accountRecords.reduce((balance, record) => balance + (record.amount || 0), 0);
    } catch (error) {
      console.error('计算账户余额错误:', error);
      return 0;
    }
  };

  // 计算资产统计
  const currentStats = useMemo(() => {
    if (!currentAssetMonth) {
      return { total: 0, byType: {}, accountDetails: [] };
    }

    let totalAssets = 0;
    const typeBalances = {};
    const accountDetails = [];

    safeAccounts.forEach(account => {
      const balance = getAccountBalanceAtMonth(account.id, currentAssetMonth);
      totalAssets += balance;

      if (!typeBalances[account.type]) {
        typeBalances[account.type] = 0;
      }
      typeBalances[account.type] += balance;

      accountDetails.push({
        id: account.id,
        name: account.name,
        type: account.type,
        balance: balance,
        target: account.target || '-'
      });
    });

    return {
      total: totalAssets,
      byType: typeBalances,
      accountDetails: accountDetails.filter(acc => acc.balance !== 0)
    };
  }, [safeAccounts, safeRecords, currentAssetMonth, getAccountBalanceAtMonth]);

  // 系统统计
  const systemStats = useMemo(() => {
    if (!safeRecords.length) {
      return {
        earliestMonth: '-',
        latestMonth: '-',
        monthsWithData: 0,
        accountCount: safeAccounts.length,
        recordCount: 0
      };
    }

    const dates = safeRecords
      .map(record => dayjs(record.date))
      .filter(date => date.isValid())
      .sort((a, b) => a.valueOf() - b.valueOf());

    const monthSet = new Set();
    safeRecords.forEach(record => {
      if (record.date) {
        const month = dayjs(record.date).format('YYYY-MM');
        monthSet.add(month);
      }
    });

    return {
      earliestMonth: dates.length > 0 ? dates[0].format('YYYY年MM月') : '-',
      latestMonth: dates.length > 0 ? dates[dates.length - 1].format('YYYY年MM月') : '-',
      monthsWithData: monthSet.size,
      accountCount: safeAccounts.length,
      recordCount: safeRecords.length
    };
  }, [safeAccounts, safeRecords]);

  // 明细表格列
  const detailColumns = [
    { title: '账户名称', dataIndex: 'name', key: 'name' },
    { title: '账户类型', dataIndex: 'type', key: 'type' },
    { title: '标的/目标', dataIndex: 'target', key: 'target' },
    {
      title: '余额',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance) => (
        <span style={{
          color: balance >= 0 ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          ¥{balance.toLocaleString()}
        </span>
      ),
      sorter: (a, b) => a.balance - b.balance,
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_, record) => {
        const percentage = currentStats.total > 0 ? (record.balance / currentStats.total * 100) : 0;
        return `${percentage.toFixed(2)}%`;
      },
    },
  ];

  // 无数据提示
  if (!safeRecords.length) {
    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>总览</Title>
        <Alert
          message="暂无数据"
          description="请先在资产管理页面添加账户和记录，然后回到此页面查看统计信息。"
          type="info"
          showIcon
          action={
            <Space>
              <Button type="primary" href="/assets">去添加数据</Button>
              <Button onClick={addTestData}>添加测试数据(刷新)</Button>
              <Button onClick={addTestDataViaContext}>添加测试数据(实时)</Button>
            </Space>
          }
        />
        <div style={{ marginTop: 16, color: '#666' }}>
          <p>调试信息：</p>
          <p>账户数量: {safeAccounts.length}</p>
          <p>记录数量: {safeRecords.length}</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>总览</Title>

      {/* 月份选择器 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card size="small" title="资产汇总设置">
            <Space>
              <CalendarOutlined />
              <span>汇总月份:</span>
              <Select
                value={currentAssetMonth}
                onChange={setCurrentAssetMonth}
                style={{ width: 200 }}
                placeholder="选择月份"
              >
                {availableMonths.map(month => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
              <Tag color="blue">共 {availableMonths.length} 个月有数据</Tag>
              <Button size="small" danger onClick={clearAllData} style={{ marginLeft: 8 }}>
                清除测试数据
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 资产概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card>
            <Statistic
              title={`总资产 (${currentAssetMonth ? dayjs(currentAssetMonth).format('YYYY年MM月') : '请选择月份'})`}
              value={currentStats.total}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix="¥"
            />
            <div style={{ marginTop: 16 }}>
              <Button
                type="primary"
                icon={<EyeOutlined />}
                onClick={() => setDetailModalVisible(true)}
                disabled={currentStats.accountDetails.length === 0}
              >
                查看明细
              </Button>
              <Tag color="blue" style={{ marginLeft: 8 }}>
                {currentStats.accountDetails.length} 个有余额账户
              </Tag>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="账户类型分布">
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(currentStats.byType)
                .sort(([,a], [,b]) => b - a)
                .map(([type, amount]) => {
                  const percentage = currentStats.total > 0 ? (amount / currentStats.total * 100) : 0;
                  return (
                    <div key={type}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                        <span>{type}</span>
                        <Space>
                          <span style={{ fontWeight: 'bold' }}>¥{amount.toLocaleString()}</span>
                          <span style={{ color: '#666' }}>({percentage.toFixed(1)}%)</span>
                        </Space>
                      </div>
                      <Progress
                        percent={percentage}
                        size="small"
                        showInfo={false}
                        strokeColor={percentage > 50 ? '#52c41a' : percentage > 20 ? '#1890ff' : '#faad14'}
                      />
                    </div>
                  );
                })}
              {Object.keys(currentStats.byType).length === 0 && (
                <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 系统统计 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="系统统计" extra={<InfoCircleOutlined />}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic title="账户数量" value={systemStats.accountCount} suffix="个" />
              </Col>
              <Col span={6}>
                <Statistic title="记录数量" value={systemStats.recordCount} suffix="条" />
              </Col>
              <Col span={6}>
                <Statistic title="有数据月份" value={systemStats.monthsWithData} suffix="个月" />
              </Col>
              <Col span={6}>
                <Space direction="vertical">
                  <div>
                    <span style={{ color: '#666' }}>最早数据:</span>
                    <br />
                    <span style={{ fontWeight: 'bold' }}>{systemStats.earliestMonth}</span>
                  </div>
                  <div>
                    <span style={{ color: '#666' }}>最新数据:</span>
                    <br />
                    <span style={{ fontWeight: 'bold' }}>{systemStats.latestMonth}</span>
                  </div>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 明细弹窗 */}
      <Modal
        title={`资产明细 - ${currentAssetMonth ? dayjs(currentAssetMonth).format('YYYY年MM月') : ''}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[<Button key="close" onClick={() => setDetailModalVisible(false)}>关闭</Button>]}
        width={800}
      >
        <div style={{ marginBottom: 16 }}>
          <Statistic
            title="总资产"
            value={currentStats.total}
            precision={2}
            prefix="¥"
            valueStyle={{ color: '#1890ff', fontSize: '24px' }}
          />
        </div>
        <Divider />
        <Table
          columns={detailColumns}
          dataSource={currentStats.accountDetails}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Modal>
    </div>
  );
}

export default Dashboard;
