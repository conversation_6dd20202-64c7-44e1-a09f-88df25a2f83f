import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Alert,
  Empty,
  Statistic,
  Space,
  Tag,
  Divider
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// 扩展dayjs插件
dayjs.extend(isSameOrBefore);

const { Title } = Typography;
const { Option } = Select;

// 数字格式化函数
const formatNumber = (value, precision = 0) => {
  if (Math.abs(value) >= 10000) {
    return `${(value / 10000).toFixed(precision)}万`;
  } else if (Math.abs(value) >= 1000) {
    return `${(value / 1000).toFixed(precision)}千`;
  }
  return value.toFixed(precision);
};

// 百分比格式化
const formatPercent = (value) => {
  if (Math.abs(value) >= 100) {
    return `${value.toFixed(0)}%`;
  } else if (Math.abs(value) >= 10) {
    return `${value.toFixed(1)}%`;
  }
  return `${value.toFixed(2)}%`;
};

// 图表颜色配置
const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff', '#ff0000'];
const ACCOUNT_TYPE_COLORS = {
  '银行卡': '#1890ff',
  '信用卡': '#ff4d4f',
  '基金': '#52c41a',
  '股票': '#722ed1',
  '理财产品': '#fa8c16',
  '其他': '#13c2c2'
};

function AssetAnalysis() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6);
  const [selectedAssetTypes, setSelectedAssetTypes] = useState(['total']); // 趋势图选择的资产类型
  const [selectedChangeTypes, setSelectedChangeTypes] = useState(['total']); // 变化分析选择的资产类型

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 获取所有可用的资产类型
  const availableAssetTypes = useMemo(() => {
    const types = [...new Set(safeAccounts.map(account => account.type))];
    return [
      { key: 'total', name: '总资产', color: '#1890ff' },
      ...types.map(type => ({
        key: type,
        name: type,
        color: ACCOUNT_TYPE_COLORS[type] || '#13c2c2'
      }))
    ];
  }, [safeAccounts]);

  // 计算每个账户在指定月份末的余额
  const getAccountBalanceAtMonth = (accountId, month) => {
    try {
      if (!month) return 0;
      const monthEnd = dayjs(month).endOf('month');
      const accountRecords = safeRecords
        .filter(record => record.accountId === accountId)
        .filter(record => dayjs(record.date).isSameOrBefore(monthEnd, 'day'));
      return accountRecords.reduce((balance, record) => balance + (record.amount || 0), 0);
    } catch (error) {
      console.error('计算账户余额错误:', error);
      return 0;
    }
  };

  // 计算选中资产类型的组合总额
  const getSelectedAssetsTotal = (monthData, selectedTypes) => {
    if (selectedTypes.includes('total')) {
      return monthData.total;
    }
    return selectedTypes.reduce((sum, type) => sum + (monthData[type] || 0), 0);
  };

  // 生成月度趋势数据
  const monthlyTrendData = useMemo(() => {
    try {
      const months = [];
      for (let i = selectedMonths - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
      }

      return months.map(month => {
        const monthData = {
          month: dayjs(month).format('MM月'),
          fullMonth: month,
          total: 0
        };

        // 按账户类型计算
        const typeBalances = {};
        safeAccounts.forEach(account => {
          const balance = getAccountBalanceAtMonth(account.id, month);
          monthData.total += balance;

          if (!typeBalances[account.type]) {
            typeBalances[account.type] = 0;
          }
          typeBalances[account.type] += balance;
        });

        // 添加各类型数据到月度数据中
        Object.entries(typeBalances).forEach(([type, balance]) => {
          monthData[type] = balance;
        });

        // 添加选中资产的组合总额
        monthData.selectedTotal = getSelectedAssetsTotal(monthData, selectedAssetTypes);
        monthData.selectedChangeTotal = getSelectedAssetsTotal(monthData, selectedChangeTypes);

        return monthData;
      });
    } catch (error) {
      console.error('生成月度趋势数据错误:', error);
      return [];
    }
  }, [safeAccounts, safeRecords, selectedMonths, selectedAssetTypes, selectedChangeTypes, getAccountBalanceAtMonth]);



  // 月度变化数据（支持分资产类型）
  const monthlyChangeData = useMemo(() => {
    try {
      if (monthlyTrendData.length < 2) return [];

      return monthlyTrendData.slice(1).map((current, index) => {
        const previous = monthlyTrendData[index];
        const result = {
          month: current.month,
          fullMonth: current.fullMonth
        };

        // 计算总资产变化
        const totalChange = current.total - previous.total;
        const totalChangePercent = previous.total !== 0 ? (totalChange / Math.abs(previous.total)) * 100 : 0;
        result.total = totalChange;
        result.totalPercent = totalChangePercent;

        // 计算选中资产组合的变化（用于变化分析图）
        const selectedChangeTotal = current.selectedChangeTotal - previous.selectedChangeTotal;
        const selectedChangeTotalPercent = previous.selectedChangeTotal !== 0 ?
          (selectedChangeTotal / Math.abs(previous.selectedChangeTotal)) * 100 : 0;
        result.selectedChangeTotal = selectedChangeTotal;
        result.selectedChangeTotalPercent = selectedChangeTotalPercent;

        // 计算各资产类型变化
        availableAssetTypes.forEach(type => {
          if (type.key !== 'total') {
            const currentValue = current[type.key] || 0;
            const previousValue = previous[type.key] || 0;
            const change = currentValue - previousValue;
            const changePercent = previousValue !== 0 ? (change / Math.abs(previousValue)) * 100 : 0;
            result[type.key] = change;
            result[`${type.key}Percent`] = changePercent;
          }
        });

        return result;
      });
    } catch (error) {
      console.error('生成月度变化数据错误:', error);
      return [];
    }
  }, [monthlyTrendData, availableAssetTypes]);

  // 如果没有数据，显示提示
  if (!safeRecords.length) {
    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>资产分析</Title>
        <Alert
          message="暂无数据"
          description="请先在资产管理页面添加账户和记录，然后回到此页面查看图表。当前资产汇总功能已移至总览页面。"
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产分析</Title>

      {/* 图表时间范围设置 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card size="small" title="分析设置">
            <Space>
              <span>时间范围:</span>
              <Select
                value={selectedMonths}
                onChange={setSelectedMonths}
                style={{ width: 150 }}
                placeholder="选择显示月份数"
              >
                <Option value={3}>最近3个月</Option>
                <Option value={6}>最近6个月</Option>
                <Option value={12}>最近12个月</Option>
                <Option value={24}>最近24个月</Option>
              </Select>
              <Tag color="blue">
                {monthlyTrendData.length > 0 ?
                  `${dayjs(monthlyTrendData[0].fullMonth).format('YYYY年MM月')} 至 ${dayjs(monthlyTrendData[monthlyTrendData.length - 1].fullMonth).format('YYYY年MM月')}` :
                  '暂无数据'}
              </Tag>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 核心指标概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title={selectedAssetTypes.includes('total') ? "当前总资产" : "选中资产总额"}
              value={monthlyTrendData.length > 0 ?
                formatNumber(selectedAssetTypes.includes('total') ?
                  monthlyTrendData[monthlyTrendData.length - 1].total :
                  monthlyTrendData[monthlyTrendData.length - 1].selectedTotal, 1) : '0'}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title={selectedChangeTypes.includes('total') ? "总资产月度变化" : "选中资产月度变化"}
              value={monthlyChangeData.length > 0 ?
                formatNumber(selectedChangeTypes.includes('total') ?
                  monthlyChangeData[monthlyChangeData.length - 1].total :
                  monthlyChangeData[monthlyChangeData.length - 1].selectedChangeTotal, 1) : '0'}
              prefix="¥"
              valueStyle={{
                color: monthlyChangeData.length > 0 &&
                  (selectedChangeTypes.includes('total') ?
                    monthlyChangeData[monthlyChangeData.length - 1].total >= 0 :
                    monthlyChangeData[monthlyChangeData.length - 1].selectedChangeTotal >= 0) ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title={selectedChangeTypes.includes('total') ? "总资产变化率" : "选中资产变化率"}
              value={monthlyChangeData.length > 0 ?
                formatPercent(selectedChangeTypes.includes('total') ?
                  monthlyChangeData[monthlyChangeData.length - 1].totalPercent :
                  monthlyChangeData[monthlyChangeData.length - 1].selectedChangeTotalPercent) : '0%'}
              valueStyle={{
                color: monthlyChangeData.length > 0 &&
                  (selectedChangeTypes.includes('total') ?
                    monthlyChangeData[monthlyChangeData.length - 1].totalPercent >= 0 :
                    monthlyChangeData[monthlyChangeData.length - 1].selectedChangeTotalPercent >= 0) ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="资产类型"
              value={availableAssetTypes.length - 1} // 减去总资产
              suffix="种"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 资产变动趋势图 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card
            title="资产变动趋势"
            extra={
              <Space>
                <Select
                  mode="multiple"
                  placeholder="选择要显示的资产类型"
                  value={selectedAssetTypes}
                  onChange={setSelectedAssetTypes}
                  style={{ minWidth: 200 }}
                  maxTagCount={2}
                >
                  {availableAssetTypes.map(type => (
                    <Option key={type.key} value={type.key}>
                      <Space>
                        <div
                          style={{
                            width: 12,
                            height: 12,
                            backgroundColor: type.color,
                            borderRadius: '50%',
                            display: 'inline-block'
                          }}
                        />
                        {type.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
                <Tag color="blue">趋势对比</Tag>
              </Space>
            }
          >
            <ResponsiveContainer width="100%" height={400}>
              <LineChart data={monthlyTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `¥${formatNumber(value)}`} />
                <Tooltip
                  formatter={(value, name) => [`¥${formatNumber(value, 1)}`, name]}
                  labelFormatter={(label) => `月份: ${label}`}
                />
                <Legend />
                {selectedAssetTypes.map(typeKey => {
                  const type = availableAssetTypes.find(t => t.key === typeKey);
                  if (!type) return null;

                  return (
                    <Line
                      key={typeKey}
                      type="monotone"
                      dataKey={typeKey}
                      stroke={type.color}
                      strokeWidth={typeKey === 'total' ? 3 : 2}
                      dot={{ fill: type.color, strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: type.color, strokeWidth: 2 }}
                      name={type.name}
                    />
                  );
                })}
                {/* 如果选择了多个非总资产类型，显示组合总额线 */}
                {selectedAssetTypes.length > 1 && !selectedAssetTypes.includes('total') && (
                  <Line
                    key="selectedTotal"
                    type="monotone"
                    dataKey="selectedTotal"
                    stroke="#ff6b35"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    dot={{ fill: "#ff6b35", strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: "#ff6b35", strokeWidth: 2 }}
                    name="选中资产组合"
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 月度变化分析 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card
            title="月度变化分析"
            extra={
              <Space>
                <Select
                  mode="multiple"
                  placeholder="选择分析的资产类型"
                  value={selectedChangeTypes}
                  onChange={setSelectedChangeTypes}
                  style={{ minWidth: 200 }}
                  maxTagCount={2}
                >
                  {availableAssetTypes.map(type => (
                    <Option key={type.key} value={type.key}>
                      <Space>
                        <div
                          style={{
                            width: 12,
                            height: 12,
                            backgroundColor: type.color,
                            borderRadius: '50%',
                            display: 'inline-block'
                          }}
                        />
                        {type.name}
                      </Space>
                    </Option>
                  ))}
                </Select>
                <Tag color="orange">环比变动</Tag>
              </Space>
            }
          >
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={monthlyChangeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `¥${formatNumber(value)}`} />
                <Tooltip
                  formatter={(value, name) => {
                    if (name.includes('变化金额')) {
                      return [`¥${formatNumber(value, 1)}`, name];
                    }
                    return [`${formatPercent(value)}`, name];
                  }}
                  labelFormatter={(label) => `月份: ${label}`}
                />
                <Legend />
                {selectedChangeTypes.map(typeKey => {
                  const type = availableAssetTypes.find(t => t.key === typeKey);
                  if (!type) return null;

                  const dataKey = typeKey === 'total' ? 'total' : typeKey;

                  return (
                    <Bar
                      key={`${typeKey}-change`}
                      dataKey={dataKey}
                      name={`${type.name}变化金额`}
                      radius={[4, 4, 0, 0]}
                    >
                      {monthlyChangeData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry[dataKey] >= 0 ? '#52c41a' : '#ff4d4f'}
                        />
                      ))}
                    </Bar>
                  );
                })}
                {/* 如果选择了多个非总资产类型，显示组合变化柱 */}
                {selectedChangeTypes.length > 1 && !selectedChangeTypes.includes('total') && (
                  <Bar
                    key="selectedChangeTotal"
                    dataKey="selectedChangeTotal"
                    name="选中资产组合变化"
                    radius={[4, 4, 0, 0]}
                  >
                    {monthlyChangeData.map((entry, index) => (
                      <Cell
                        key={`cell-combo-${index}`}
                        fill={entry.selectedChangeTotal >= 0 ? '#ff6b35' : '#ff4757'}
                      />
                    ))}
                  </Bar>
                )}
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 数据洞察 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="数据洞察" extra={<Tag color="purple">智能分析</Tag>}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }}>
                    {monthlyTrendData.length > 0 ? formatNumber(monthlyTrendData[monthlyTrendData.length - 1].total, 1) : '0'}
                  </div>
                  <div style={{ color: '#666' }}>当前总资产</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    最新月份资产总额
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }}>
                    {monthlyChangeData.filter(item => item.total > 0).length}
                  </div>
                  <div style={{ color: '#666' }}>增长月份数</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    共 {monthlyChangeData.length} 个月数据
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#722ed1', marginBottom: '8px' }}>
                    {monthlyChangeData.length > 0 ?
                      formatPercent(Math.max(...monthlyChangeData.map(item => Math.abs(item.totalPercent)))) :
                      '0%'}
                  </div>
                  <div style={{ color: '#666' }}>最大变化率</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    单月最大波动
                  </div>
                </div>
              </Col>
            </Row>
            <Divider />
            <div style={{ color: '#666' }}>
              <p><strong>功能特性：</strong></p>
              <Space wrap>
                <Tag color="blue">📊 交互式趋势分析</Tag>
                <Tag color="green">🎯 多资产类型对比</Tag>
                <Tag color="orange">📈 分类变动追踪</Tag>
                <Tag color="purple">💡 智能数据洞察</Tag>
                <Tag color="cyan">📅 灵活时间筛选</Tag>
                <Tag color="red">🔄 实时数据更新</Tag>
              </Space>
              <p style={{ marginTop: '16px' }}><strong>使用说明：</strong></p>
              <ul style={{ marginBottom: 0 }}>
                <li>趋势图支持多选资产类型，可同时对比不同资产的变化</li>
                <li>总资产用粗线显示，分类资产用细线，便于区分</li>
                <li>月度变化分析支持切换不同资产类型的变动情况</li>
                <li>所有数字自动格式化，大数字显示为万/千单位</li>
                <li>变化率根据数值大小自动调整小数位数</li>
                <li>颜色编码：绿色表示增长，红色表示下降</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default AssetAnalysis;
