import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Alert,
  Empty,
  Statistic,
  Space,
  Tag,
  Divider
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// 扩展dayjs插件
dayjs.extend(isSameOrBefore);

const { Title } = Typography;
const { Option } = Select;

// 图表颜色配置
const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff', '#00ffff', '#ff0000'];
const ACCOUNT_TYPE_COLORS = {
  '银行卡': '#1890ff',
  '信用卡': '#ff4d4f',
  '基金': '#52c41a',
  '股票': '#722ed1',
  '理财产品': '#fa8c16',
  '其他': '#13c2c2'
};

function AssetAnalysis() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6);

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 计算每个账户在指定月份末的余额
  const getAccountBalanceAtMonth = (accountId, month) => {
    try {
      if (!month) return 0;
      const monthEnd = dayjs(month).endOf('month');
      const accountRecords = safeRecords
        .filter(record => record.accountId === accountId)
        .filter(record => dayjs(record.date).isSameOrBefore(monthEnd, 'day'));
      return accountRecords.reduce((balance, record) => balance + (record.amount || 0), 0);
    } catch (error) {
      console.error('计算账户余额错误:', error);
      return 0;
    }
  };

  // 生成月度趋势数据
  const monthlyTrendData = useMemo(() => {
    try {
      const months = [];
      for (let i = selectedMonths - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
      }

      return months.map(month => {
        const monthData = {
          month: dayjs(month).format('MM月'),
          fullMonth: month,
          total: 0
        };

        // 按账户类型计算
        const typeBalances = {};
        safeAccounts.forEach(account => {
          const balance = getAccountBalanceAtMonth(account.id, month);
          monthData.total += balance;

          if (!typeBalances[account.type]) {
            typeBalances[account.type] = 0;
          }
          typeBalances[account.type] += balance;
        });

        // 添加各类型数据到月度数据中
        Object.entries(typeBalances).forEach(([type, balance]) => {
          monthData[type] = balance;
        });

        return monthData;
      });
    } catch (error) {
      console.error('生成月度趋势数据错误:', error);
      return [];
    }
  }, [safeAccounts, safeRecords, selectedMonths, getAccountBalanceAtMonth]);

  // 当前月份的资产分布数据（饼图用）
  const currentAssetDistribution = useMemo(() => {
    try {
      const currentMonth = dayjs().format('YYYY-MM');
      const typeBalances = {};

      safeAccounts.forEach(account => {
        const balance = getAccountBalanceAtMonth(account.id, currentMonth);
        if (balance > 0) { // 只显示正资产
          if (!typeBalances[account.type]) {
            typeBalances[account.type] = 0;
          }
          typeBalances[account.type] += balance;
        }
      });

      return Object.entries(typeBalances)
        .map(([type, value]) => ({
          name: type,
          value: value,
          color: ACCOUNT_TYPE_COLORS[type] || '#13c2c2'
        }))
        .sort((a, b) => b.value - a.value);
    } catch (error) {
      console.error('生成资产分布数据错误:', error);
      return [];
    }
  }, [safeAccounts, safeRecords, getAccountBalanceAtMonth]);

  // 月度变化数据（柱状图用）
  const monthlyChangeData = useMemo(() => {
    try {
      if (monthlyTrendData.length < 2) return [];

      return monthlyTrendData.slice(1).map((current, index) => {
        const previous = monthlyTrendData[index];
        const change = current.total - previous.total;
        const changePercent = previous.total !== 0 ? (change / Math.abs(previous.total)) * 100 : 0;

        return {
          month: current.month,
          change: change,
          changePercent: changePercent,
          total: current.total
        };
      });
    } catch (error) {
      console.error('生成月度变化数据错误:', error);
      return [];
    }
  }, [monthlyTrendData]);

  // 如果没有数据，显示提示
  if (!safeRecords.length) {
    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>资产分析</Title>
        <Alert
          message="暂无数据"
          description="请先在资产管理页面添加账户和记录，然后回到此页面查看图表。当前资产汇总功能已移至总览页面。"
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>资产分析</Title>

      {/* 图表时间范围设置 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card size="small" title="分析设置">
            <Space>
              <span>时间范围:</span>
              <Select
                value={selectedMonths}
                onChange={setSelectedMonths}
                style={{ width: 150 }}
                placeholder="选择显示月份数"
              >
                <Option value={3}>最近3个月</Option>
                <Option value={6}>最近6个月</Option>
                <Option value={12}>最近12个月</Option>
                <Option value={24}>最近24个月</Option>
              </Select>
              <Tag color="blue">
                {monthlyTrendData.length > 0 ?
                  `${dayjs(monthlyTrendData[0].fullMonth).format('YYYY年MM月')} 至 ${dayjs(monthlyTrendData[monthlyTrendData.length - 1].fullMonth).format('YYYY年MM月')}` :
                  '暂无数据'}
              </Tag>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 核心指标概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="当前总资产"
              value={monthlyTrendData.length > 0 ? monthlyTrendData[monthlyTrendData.length - 1].total : 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="月度变化"
              value={monthlyChangeData.length > 0 ? monthlyChangeData[monthlyChangeData.length - 1].change : 0}
              precision={2}
              prefix="¥"
              valueStyle={{
                color: monthlyChangeData.length > 0 && monthlyChangeData[monthlyChangeData.length - 1].change >= 0 ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="变化率"
              value={monthlyChangeData.length > 0 ? monthlyChangeData[monthlyChangeData.length - 1].changePercent : 0}
              precision={2}
              suffix="%"
              valueStyle={{
                color: monthlyChangeData.length > 0 && monthlyChangeData[monthlyChangeData.length - 1].changePercent >= 0 ? '#52c41a' : '#ff4d4f'
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card hoverable>
            <Statistic
              title="资产类型"
              value={currentAssetDistribution.length}
              suffix="种"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 资产变动趋势图 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="资产变动趋势" extra={<Tag color="blue">总资产及分类趋势</Tag>}>
            <ResponsiveContainer width="100%" height={400}>
              <AreaChart data={monthlyTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `¥${(value / 1000).toFixed(1)}k`} />
                <Tooltip
                  formatter={(value, name) => [`¥${value.toLocaleString()}`, name]}
                  labelFormatter={(label) => `月份: ${label}`}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="total"
                  stackId="1"
                  stroke="#1890ff"
                  fill="#1890ff"
                  fillOpacity={0.3}
                  name="总资产"
                />
                {Object.keys(ACCOUNT_TYPE_COLORS).map((type, index) => (
                  <Area
                    key={type}
                    type="monotone"
                    dataKey={type}
                    stackId="2"
                    stroke={ACCOUNT_TYPE_COLORS[type]}
                    fill={ACCOUNT_TYPE_COLORS[type]}
                    fillOpacity={0.6}
                    name={type}
                  />
                ))}
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 资产分布和变动分析 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="当前资产分布" extra={<Tag color="green">按类型占比</Tag>} style={{ marginBottom: 16 }}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={currentAssetDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {currentAssetDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`¥${value.toLocaleString()}`, '金额']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="月度变化分析" extra={<Tag color="orange">环比变动</Tag>}>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyChangeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `¥${(value / 1000).toFixed(1)}k`} />
                <Tooltip
                  formatter={(value, name) => {
                    if (name === 'change') {
                      return [`¥${value.toLocaleString()}`, '变化金额'];
                    }
                    return [`${value.toFixed(2)}%`, '变化率'];
                  }}
                />
                <Legend />
                <Bar
                  dataKey="change"
                  name="变化金额"
                  radius={[4, 4, 0, 0]}
                >
                  {monthlyChangeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.change >= 0 ? '#52c41a' : '#ff4d4f'} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 数据洞察 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="数据洞察" extra={<Tag color="purple">智能分析</Tag>}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }}>
                    {currentAssetDistribution.length > 0 ? currentAssetDistribution[0].name : '-'}
                  </div>
                  <div style={{ color: '#666' }}>最大资产类型</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    {currentAssetDistribution.length > 0 ?
                      `占比 ${((currentAssetDistribution[0].value / currentAssetDistribution.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%` :
                      '暂无数据'}
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }}>
                    {monthlyChangeData.filter(item => item.change > 0).length}
                  </div>
                  <div style={{ color: '#666' }}>增长月份数</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    共 {monthlyChangeData.length} 个月数据
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ fontSize: '24px', color: '#722ed1', marginBottom: '8px' }}>
                    {monthlyChangeData.length > 0 ?
                      Math.max(...monthlyChangeData.map(item => Math.abs(item.changePercent))).toFixed(1) :
                      '0'}%
                  </div>
                  <div style={{ color: '#666' }}>最大变化率</div>
                  <div style={{ fontSize: '14px', color: '#999', marginTop: '4px' }}>
                    单月最大波动
                  </div>
                </div>
              </Col>
            </Row>
            <Divider />
            <div style={{ color: '#666' }}>
              <p><strong>功能特性：</strong></p>
              <Space wrap>
                <Tag color="blue">📊 实时趋势分析</Tag>
                <Tag color="green">🥧 资产分布可视化</Tag>
                <Tag color="orange">📈 环比变动追踪</Tag>
                <Tag color="purple">💡 智能数据洞察</Tag>
                <Tag color="cyan">📅 多时间维度分析</Tag>
              </Space>
              <p style={{ marginTop: '16px' }}><strong>使用说明：</strong></p>
              <ul style={{ marginBottom: 0 }}>
                <li>调整时间范围可查看不同周期的资产变化趋势</li>
                <li>趋势图支持总资产和分类资产的对比分析</li>
                <li>饼图显示当前月份各类资产的分布情况</li>
                <li>柱状图展示月度资产变化的绝对金额</li>
                <li>数据洞察提供关键指标的智能分析</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default AssetAnalysis;
