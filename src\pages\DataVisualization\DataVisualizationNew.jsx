import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Typography,
  Alert,
  Empty
} from 'antd';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Option } = Select;

function DataVisualization() {
  const { accounts, records } = useAsset();
  const [selectedMonths, setSelectedMonths] = useState(6);

  // 安全的数据处理
  const safeAccounts = accounts || [];
  const safeRecords = records || [];

  // 生成月度数据（用于图表）
  const monthlyData = useMemo(() => {
    try {
      const months = [];
      for (let i = selectedMonths - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, 'month').format('YYYY-MM'));
      }
      return months;
    } catch (error) {
      console.error('生成月度数据错误:', error);
      return [];
    }
  }, [selectedMonths]);

  // 如果没有数据，显示提示
  if (!safeRecords.length) {
    return (
      <div style={{ padding: '24px' }}>
        <Title level={2}>数据可视化</Title>
        <Alert
          message="暂无数据"
          description="请先在资产管理页面添加账户和记录，然后回到此页面查看图表。当前资产汇总功能已移至总览页面。"
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据可视化</Title>
      
      <Alert
        message="功能重新组织"
        description="当前资产汇总、账户类型分布、系统统计等功能已移至总览页面。此页面专注于图表功能开发。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 图表时间范围设置 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card size="small" title="图表时间范围设置">
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <span>显示范围:</span>
              <Select
                value={selectedMonths}
                onChange={setSelectedMonths}
                style={{ width: 150 }}
                placeholder="选择显示月份数"
              >
                <Option value={3}>最近3个月</Option>
                <Option value={6}>最近6个月</Option>
                <Option value={12}>最近12个月</Option>
                <Option value={24}>最近24个月</Option>
              </Select>
              <span style={{ color: '#666', fontSize: '12px' }}>
                ({monthlyData.length > 0 ? 
                  `${dayjs(monthlyData[0]).format('YYYY年MM月')} 至 ${dayjs(monthlyData[monthlyData.length - 1]).format('YYYY年MM月')}` : 
                  '暂无数据'})
              </span>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Card title="资产变动趋势图（开发中）">
            <div style={{ 
              height: '400px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              border: '2px dashed #d9d9d9'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
                <div style={{ fontSize: '18px', color: '#666', marginBottom: '8px' }}>资产变动趋势图</div>
                <div style={{ fontSize: '14px', color: '#999', marginBottom: '16px' }}>
                  显示总资产和各类别资产的月度变化曲线
                </div>
                <div style={{ fontSize: '12px', color: '#bbb' }}>
                  时间范围: {monthlyData.length > 0 ? 
                    `${dayjs(monthlyData[0]).format('MM月')} - ${dayjs(monthlyData[monthlyData.length - 1]).format('MM月')}` : 
                    '暂无数据'}
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="资产分布饼图（开发中）">
            <div style={{ 
              height: '300px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              border: '2px dashed #d9d9d9'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '36px', marginBottom: '12px' }}>🥧</div>
                <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>资产分布饼图</div>
                <div style={{ fontSize: '12px', color: '#999' }}>
                  交互式饼图显示各类资产占比
                </div>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="环比变动分析（开发中）">
            <div style={{ 
              height: '300px', 
              background: '#f5f5f5', 
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              border: '2px dashed #d9d9d9'
            }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '36px', marginBottom: '12px' }}>📈</div>
                <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>环比变动分析</div>
                <div style={{ fontSize: '12px', color: '#999' }}>
                  月度资产变化率和变化金额
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 功能说明 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="功能说明">
            <div style={{ color: '#666' }}>
              <p><strong>已移至总览页面的功能：</strong></p>
              <ul>
                <li>✅ 当前资产汇总（支持选择汇总月份）</li>
                <li>✅ 账户类型分布（合并显示金额和占比）</li>
                <li>✅ 资产明细查看（弹窗显示详细信息）</li>
                <li>✅ 系统统计（最早最晚数据月份、有数据月份数量等）</li>
              </ul>
              <p><strong>此页面即将推出的图表功能：</strong></p>
              <ul>
                <li>📊 资产变动趋势图（总资产和各类别资产的月度变化曲线）</li>
                <li>🥧 交互式资产分布饼图</li>
                <li>📈 环比变动分析图表</li>
                <li>📅 月度数据对比分析</li>
              </ul>
              <p><strong>使用说明：</strong></p>
              <ul>
                <li>时间筛选器仅影响图表显示范围，不影响总览页面的资产汇总</li>
                <li>图表将基于历史记录计算月末资产余额</li>
                <li>支持按账户类型分别显示变化趋势</li>
              </ul>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualization;
