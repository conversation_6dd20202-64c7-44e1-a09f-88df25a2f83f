import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { useAsset } from '../../context/AssetContext';

const { Option } = Select;

const accountTypes = [
  '银行卡',
  '基金',
  '理财产品',
  '股票',
  '信用卡',
  '其他'
];

function AccountModal({ visible, onCancel, account }) {
  const [form] = Form.useForm();
  const { accounts, addAccount, updateAccount } = useAsset();
  const [selectedAccountType, setSelectedAccountType] = useState('银行卡');

  useEffect(() => {
    if (visible) {
      if (account) {
        form.setFieldsValue(account);
        setSelectedAccountType(account.type || '银行卡');
      } else {
        form.resetFields();
        setSelectedAccountType('银行卡');
      }
    }
  }, [visible, account, form]);

  // 判断是否为投资类账户，需要显示标的字段
  const isInvestmentAccount = (type) => {
    return ['基金', '理财产品', '股票'].includes(type);
  };

  // 获取标的字段的标签和占位符
  const getTargetFieldInfo = (type) => {
    const infoMap = {
      '基金': { label: '基金代码/名称', placeholder: '请输入基金代码或基金名称，如：000001 华夏成长' },
      '理财产品': { label: '产品名称/代码', placeholder: '请输入理财产品名称或代码' },
      '股票': { label: '股票代码/名称', placeholder: '请输入股票代码或股票名称，如：000001 平安银行' }
    };
    return infoMap[type] || { label: '标的信息', placeholder: '请输入标的信息' };
  };

  // 检查账户名称是否重复
  const checkAccountNameDuplicate = (_, value) => {
    if (!value) {
      return Promise.resolve();
    }

    const existingAccount = accounts.find(acc =>
      acc.name.toLowerCase() === value.toLowerCase() &&
      acc.id !== account?.id // 编辑时排除自己
    );

    if (existingAccount) {
      return Promise.reject(new Error('账户名称已存在，请使用其他名称'));
    }

    return Promise.resolve();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (account) {
        updateAccount({ ...account, ...values });
        message.success('账户更新成功');
      } else {
        addAccount(values);
        message.success('账户添加成功');
      }

      onCancel();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={account ? '编辑账户' : '添加账户'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      okText="确定"
      cancelText="取消"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: '银行卡'
        }}
      >
        <Form.Item
          name="name"
          label="账户名称"
          rules={[
            { required: true, message: '请输入账户名称' },
            { max: 50, message: '账户名称不能超过50个字符' },
            { validator: checkAccountNameDuplicate }
          ]}
        >
          <Input placeholder="请输入账户名称" />
        </Form.Item>

        <Form.Item
          name="type"
          label="账户类型"
          rules={[{ required: true, message: '请选择账户类型' }]}
        >
          <Select
            placeholder="请选择账户类型"
            onChange={(value) => {
              setSelectedAccountType(value);
              // 切换账户类型时清空标的字段
              if (isInvestmentAccount(value)) {
                form.setFieldsValue({ target: undefined });
              }
            }}
          >
            {accountTypes.map(type => (
              <Option key={type} value={type}>
                {type}
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* 投资类账户的标的字段 */}
        {isInvestmentAccount(selectedAccountType) && (
          <Form.Item
            name="target"
            label={getTargetFieldInfo(selectedAccountType).label}
            rules={[
              { required: true, message: `请输入${getTargetFieldInfo(selectedAccountType).label}` },
              { max: 100, message: '标的信息不能超过100个字符' }
            ]}
          >
            <Input placeholder={getTargetFieldInfo(selectedAccountType).placeholder} />
          </Form.Item>
        )}

        <Form.Item
          name="note"
          label="备注"
          rules={[{ max: 200, message: '备注不能超过200个字符' }]}
        >
          <Input.TextArea
            rows={3}
            placeholder="请输入备注信息（可选）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default AccountModal;
