import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, message } from 'antd';
import { useAsset } from '../../context/AssetContext';

const { Option } = Select;

const accountTypes = [
  '银行卡',
  '基金',
  '理财产品',
  '股票',
  '信用卡',
  '其他'
];

function AccountModal({ visible, onCancel, account }) {
  const [form] = Form.useForm();
  const { accounts, addAccount, updateAccount } = useAsset();

  useEffect(() => {
    if (visible) {
      if (account) {
        form.setFieldsValue(account);
      } else {
        form.resetFields();
      }
    }
  }, [visible, account, form]);

  // 检查账户名称是否重复
  const checkAccountNameDuplicate = (_, value) => {
    if (!value) {
      return Promise.resolve();
    }

    const existingAccount = accounts.find(acc =>
      acc.name.toLowerCase() === value.toLowerCase() &&
      acc.id !== account?.id // 编辑时排除自己
    );

    if (existingAccount) {
      return Promise.reject(new Error('账户名称已存在，请使用其他名称'));
    }

    return Promise.resolve();
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (account) {
        updateAccount({ ...account, ...values });
        message.success('账户更新成功');
      } else {
        addAccount(values);
        message.success('账户添加成功');
      }

      onCancel();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title={account ? '编辑账户' : '添加账户'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      okText="确定"
      cancelText="取消"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: '银行卡'
        }}
      >
        <Form.Item
          name="name"
          label="账户名称"
          rules={[
            { required: true, message: '请输入账户名称' },
            { max: 50, message: '账户名称不能超过50个字符' },
            { validator: checkAccountNameDuplicate }
          ]}
        >
          <Input placeholder="请输入账户名称" />
        </Form.Item>

        <Form.Item
          name="type"
          label="账户类型"
          rules={[{ required: true, message: '请选择账户类型' }]}
        >
          <Select placeholder="请选择账户类型">
            {accountTypes.map(type => (
              <Option key={type} value={type}>
                {type}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="note"
          label="备注"
          rules={[{ max: 200, message: '备注不能超过200个字符' }]}
        >
          <Input.TextArea
            rows={3}
            placeholder="请输入备注信息（可选）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default AccountModal;
