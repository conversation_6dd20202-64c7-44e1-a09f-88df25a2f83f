import React, { useState } from 'react';
import { Layout as AntLayout, <PERSON>u, Typo<PERSON>, <PERSON><PERSON>, But<PERSON> } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  BankOutlined,
  BarChartOutlined,
  LineChartOutlined,
  MenuOutlined
} from '@ant-design/icons';
import './Layout.css';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '总览'
  },
  {
    key: '/assets',
    icon: <BankOutlined />,
    label: '资产管理'
  },
  {
    key: '/visualization',
    icon: <BarChartOutlined />,
    label: '数据可视化'
  },
  {
    key: '/analysis',
    icon: <LineChartOutlined />,
    label: '投资分析'
  }
];

function Layout({ children }) {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const navigate = useNavigate();
  const location = useLocation();

  React.useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const SideMenu = () => (
    <Menu
      theme="dark"
      mode="inline"
      selectedKeys={[location.pathname]}
      items={menuItems}
      onClick={handleMenuClick}
    />
  );

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          style={{
            position: 'fixed',
            height: '100vh',
            left: 0,
            top: 0,
            zIndex: 100
          }}
        >
          <div style={{
            height: '64px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: 'rgba(255, 255, 255, 0.1)',
            margin: '16px',
            borderRadius: '6px'
          }}>
            <Title level={4} style={{ color: 'white', margin: 0 }}>
              {collapsed ? '家财' : '家财管家'}
            </Title>
          </div>
          <SideMenu />
        </Sider>
      )}

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="家财管家"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        bodyStyle={{ padding: 0, background: '#001529' }}
      >
        <SideMenu />
      </Drawer>

      <AntLayout style={{ marginLeft: isMobile ? 0 : (collapsed ? 80 : 200) }}>
        <Header style={{
          background: '#001529',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          position: 'fixed',
          width: isMobile ? '100%' : `calc(100% - ${collapsed ? 80 : 200}px)`,
          top: 0,
          zIndex: 99,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}>
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => {
              if (isMobile) {
                setMobileMenuVisible(true);
              } else {
                setCollapsed(!collapsed);
              }
            }}
            style={{
              color: 'white',
              fontSize: '18px',
              marginRight: '16px'
            }}
          />
          <Title level={3} style={{ margin: 0, color: 'white' }}>
            家财管家
          </Title>
        </Header>

        <Content style={{
          marginTop: '64px',
          padding: isMobile ? '16px' : '24px',
          background: '#f0f2f5',
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
}

export default Layout;
