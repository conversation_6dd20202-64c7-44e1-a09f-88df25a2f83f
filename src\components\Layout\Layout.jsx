import React, { useState } from 'react';
import { Layout as AntLayout, <PERSON>u, Typo<PERSON>, <PERSON><PERSON>, But<PERSON> } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  BankOutlined,
  BarChartOutlined,
  LineChartOutlined,
  MenuOutlined
} from '@ant-design/icons';
import './Layout.css';

const { Header, Sider, Content } = AntLayout;
const { Title } = Typography;

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: '总览'
  },
  {
    key: '/assets',
    icon: <BankOutlined />,
    label: '资产管理'
  },
  {
    key: '/visualization',
    icon: <BarChartOutlined />,
    label: '数据可视化'
  },
  {
    key: '/analysis',
    icon: <LineChartOutlined />,
    label: '投资分析'
  }
];

function Layout({ children }) {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const handleMenuClick = ({ key }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const SideMenu = () => (
    <Menu
      theme="dark"
      mode="inline"
      selectedKeys={[location.pathname]}
      items={menuItems}
      onClick={handleMenuClick}
    />
  );

  return (
    <AntLayout className="layout">
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="desktop-sider"
        breakpoint="lg"
        onBreakpoint={(broken) => {
          if (broken) {
            setCollapsed(true);
          }
        }}
      >
        <div className="logo">
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            {collapsed ? '家财' : '家财管家'}
          </Title>
        </div>
        <SideMenu />
      </Sider>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title="家财管家"
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        className="mobile-drawer"
        bodyStyle={{ padding: 0 }}
      >
        <SideMenu />
      </Drawer>

      <AntLayout>
        <Header className="header">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => {
              if (window.innerWidth <= 768) {
                setMobileMenuVisible(true);
              } else {
                setCollapsed(!collapsed);
              }
            }}
            className="menu-trigger"
          />
          <Title level={3} style={{ margin: 0, color: 'white' }}>
            家财管家
          </Title>
        </Header>
        
        <Content className="content">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
}

export default Layout;
