import React from 'react';
import {
  Table,
  Tag,
  Button,
  Space,
  Popconfirm,
  Empty,
  message
} from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useAsset } from '../../context/AssetContext';

function AccountList({ onEdit }) {
  const { accounts, records, deleteAccount } = useAsset();

  const getTypeColor = (type) => {
    const colorMap = {
      '银行卡': 'blue',
      '基金': 'green',
      '理财产品': 'orange',
      '股票': 'red',
      '信用卡': 'purple',
      '其他': 'default'
    };
    return colorMap[type] || 'default';
  };

  const getLatestAmount = (accountId) => {
    const accountRecords = records
      .filter(record => record.accountId === accountId)
      .sort((a, b) => new Date(b.date) - new Date(a.date));

    return accountRecords.length > 0 ? accountRecords[0].amount : 0;
  };

  const getRecordCount = (accountId) => {
    return records.filter(record => record.accountId === accountId).length;
  };

  const handleDelete = (accountId) => {
    const recordCount = getRecordCount(accountId);
    if (recordCount > 0) {
      message.warning(`该账户有 ${recordCount} 条记录，删除账户将同时删除所有相关记录`);
    }
    deleteAccount(accountId);
    message.success('删除成功');
  };

  const isInvestmentAccount = (type) => {
    return ['基金', '理财产品', '股票'].includes(type);
  };

  const columns = [
    {
      title: '账户名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
    },
    {
      title: '账户类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => (
        <Tag color={getTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '标的信息',
      dataIndex: 'target',
      key: 'target',
      width: 200,
      ellipsis: true,
      render: (target, record) => {
        if (!isInvestmentAccount(record.type) || !target) {
          return '-';
        }
        return (
          <span title={target} style={{ color: '#1890ff' }}>
            {target}
          </span>
        );
      },
    },
    {
      title: '最新余额',
      key: 'latestAmount',
      width: 130,
      render: (_, record) => {
        const amount = getLatestAmount(record.id);
        return (
          <span style={{ fontWeight: 'bold' }}>
            ¥{amount.toLocaleString()}
          </span>
        );
      },
    },
    {
      title: '记录数量',
      key: 'recordCount',
      width: 80,
      render: (_, record) => getRecordCount(record.id),
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      ellipsis: true,
      width: 150,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(record)}
            size="small"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个账户吗？"
            description={
              getRecordCount(record.id) > 0
                ? `将同时删除 ${getRecordCount(record.id)} 条相关记录`
                : '删除后无法恢复'
            }
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={accounts}
      rowKey="id"
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 个账户`,
      }}
      locale={{
        emptyText: (
          <Empty
            description="暂无账户"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ),
      }}
      scroll={{ x: 800 }}
    />
  );
}

export default AccountList;
