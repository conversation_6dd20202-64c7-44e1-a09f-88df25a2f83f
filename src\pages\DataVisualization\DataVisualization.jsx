import React, { useState, useMemo } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Typography,
  Statistic,
  Space
} from 'antd';
import ReactECharts from 'echarts-for-react';
import { useAsset } from '../../context/AssetContext';
import dayjs from 'dayjs';
import './DataVisualization.css';

const { Title } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

function DataVisualization() {
  const { accounts, records } = useAsset();
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(6, 'month'),
    dayjs()
  ]);
  const [selectedAccountType, setSelectedAccountType] = useState('all');

  // 获取账户类型列表
  const accountTypes = useMemo(() => {
    const types = new Set(accounts.map(acc => acc.type));
    return Array.from(types);
  }, [accounts]);

  // 过滤记录
  const filteredRecords = useMemo(() => {
    return records.filter(record => {
      const recordDate = dayjs(record.date);
      const inDateRange = recordDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');

      if (!inDateRange) return false;

      if (selectedAccountType === 'all') return true;

      const account = accounts.find(acc => acc.id === record.accountId);
      return account && account.type === selectedAccountType;
    });
  }, [records, accounts, dateRange, selectedAccountType]);

  // 计算统计数据
  const statistics = useMemo(() => {
    const totalIncome = filteredRecords
      .filter(record => record.amount > 0)
      .reduce((sum, record) => sum + record.amount, 0);

    const totalExpense = filteredRecords
      .filter(record => record.amount < 0)
      .reduce((sum, record) => sum + Math.abs(record.amount), 0);

    const netIncome = totalIncome - totalExpense;

    return { totalIncome, totalExpense, netIncome };
  }, [filteredRecords]);

  // 月度趋势数据
  const monthlyTrendData = useMemo(() => {
    const monthlyData = {};

    filteredRecords.forEach(record => {
      const month = dayjs(record.date).format('YYYY-MM');
      if (!monthlyData[month]) {
        monthlyData[month] = { income: 0, expense: 0 };
      }

      if (record.amount > 0) {
        monthlyData[month].income += record.amount;
      } else {
        monthlyData[month].expense += Math.abs(record.amount);
      }
    });

    const months = Object.keys(monthlyData).sort();
    const incomeData = months.map(month => monthlyData[month].income);
    const expenseData = months.map(month => monthlyData[month].expense);

    return {
      months: months.map(month => dayjs(month).format('MM月')),
      incomeData,
      expenseData
    };
  }, [filteredRecords]);

  // 账户类型分布数据
  const accountTypeDistribution = useMemo(() => {
    const typeData = {};

    accounts.forEach(account => {
      const accountRecords = filteredRecords.filter(record => record.accountId === account.id);
      const balance = accountRecords.reduce((sum, record) => sum + record.amount, 0);

      if (!typeData[account.type]) {
        typeData[account.type] = 0;
      }
      typeData[account.type] += balance;
    });

    return Object.entries(typeData)
      .filter(([_, value]) => value > 0)
      .map(([name, value]) => ({ name, value }));
  }, [accounts, filteredRecords]);

  // 月度趋势图配置
  const monthlyTrendOption = {
    title: {
      text: '月度收支趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        let result = params[0].name + '<br/>';
        params.forEach(param => {
          result += param.marker + param.seriesName + ': ¥' + param.value.toLocaleString() + '<br/>';
        });
        return result;
      }
    },
    legend: {
      data: ['收入', '支出'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: monthlyTrendData.months
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    series: [
      {
        name: '收入',
        type: 'line',
        data: monthlyTrendData.incomeData,
        itemStyle: { color: '#52c41a' },
        areaStyle: { opacity: 0.3 }
      },
      {
        name: '支出',
        type: 'line',
        data: monthlyTrendData.expenseData,
        itemStyle: { color: '#ff4d4f' },
        areaStyle: { opacity: 0.3 }
      }
    ]
  };

  // 账户类型分布饼图配置
  const accountDistributionOption = {
    title: {
      text: '账户资产分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle'
    },
    series: [
      {
        name: '资产分布',
        type: 'pie',
        radius: '50%',
        center: ['60%', '50%'],
        data: accountTypeDistribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>数据可视化</Title>

      {/* 筛选器 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Col>
        <Col span={8}>
          <Select
            value={selectedAccountType}
            onChange={setSelectedAccountType}
            style={{ width: '100%' }}
            placeholder="选择账户类型"
          >
            <Option value="all">全部账户类型</Option>
            {accountTypes.map(type => (
              <Option key={type} value={type}>{type}</Option>
            ))}
          </Select>
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总收入"
              value={statistics.totalIncome}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="总支出"
              value={statistics.totalExpense}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix="¥"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="净收入"
              value={statistics.netIncome}
              precision={2}
              valueStyle={{ color: statistics.netIncome >= 0 ? '#3f8600' : '#cf1322' }}
              prefix="¥"
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16}>
        <Col span={24} style={{ marginBottom: 24 }}>
          <Card title="月度收支趋势">
            <ReactECharts option={monthlyTrendOption} style={{ height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="账户资产分布">
            <ReactECharts option={accountDistributionOption} style={{ height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="记录统计">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic title="总记录数" value={filteredRecords.length} suffix="条" />
              <Statistic title="收入记录" value={filteredRecords.filter(r => r.amount > 0).length} suffix="条" />
              <Statistic title="支出记录" value={filteredRecords.filter(r => r.amount < 0).length} suffix="条" />
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualization;
