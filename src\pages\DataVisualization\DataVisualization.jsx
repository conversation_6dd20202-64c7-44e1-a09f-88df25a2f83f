import React, { useContext } from 'react';
import { Typo<PERSON>, Card, Row, Col } from 'antd';
import { <PERSON><PERSON><PERSON>, Pie, Cell, LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { DataContext } from '../../context/DataContext';

const { Title } = Typography;

function DataVisualization() {
  const { accounts, records } = useContext(DataContext);

  // 计算账户类型分布
  const accountTypeData = accounts.reduce((acc, account) => {
    const existing = acc.find(item => item.name === account.type);
    if (existing) {
      existing.value += account.balance;
    } else {
      acc.push({ name: account.type, value: account.balance });
    }
    return acc;
  }, []);

  // 计算资产记录趋势（模拟数据）
  const trendData = [
    { month: '1月', value: 25000 },
    { month: '2月', value: 28000 },
    { month: '3月', value: 32000 },
    { month: '4月', value: 30000 },
    { month: '5月', value: 35000 },
    { month: '6月', value: 36814 }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div style={{
      width: '100%',
      height: '100%',
      padding: '0'
    }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>数据可视化</Title>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="账户类型分布" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={accountTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {accountTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`¥${value.toLocaleString()}`, '金额']} />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="资产记录趋势" style={{ height: '400px' }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`¥${value.toLocaleString()}`, '总资产']} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={2}
                  name="总资产"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualization;
