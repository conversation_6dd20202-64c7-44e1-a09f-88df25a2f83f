import { Typography, Card, Row, Col, Statistic } from 'antd';
import { useAsset } from '../../context/AssetContext';

const { Title } = Typography;

function DataVisualization() {
  const { accounts, records } = useAsset();

  // 计算账户类型分布
  const accountTypeStats = accounts.reduce((acc, account) => {
    acc[account.type] = (acc[account.type] || 0) + 1;
    return acc;
  }, {});

  return (
    <div style={{
      width: '100%',
      height: '100%',
      padding: '0'
    }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>数据可视化</Title>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="账户类型分布">
            {Object.entries(accountTypeStats).map(([type, count]) => (
              <div key={type} style={{ marginBottom: '16px' }}>
                <Statistic title={type} value={count} suffix="个账户" />
              </div>
            ))}
            {accounts.length === 0 && <p>暂无账户数据</p>}
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="记录统计">
            <Statistic title="总记录数" value={records.length} suffix="条" />
            <div style={{ marginTop: '16px' }}>
              <p>图表功能开发中...</p>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
}

export default DataVisualization;
